package ca.xef5000.ariesCoinflip.managers;

import ca.xef5000.ariesCoinflip.AriesCoinflip;
import ca.xef5000.ariesCoinflip.models.currencies.Currency;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

public class EscrowManager {
    // Map of player UUID -> currency -> balance
    private final Map<UUID, Map<String, Double>> escrowBalances = new HashMap<>();
    private final AriesCoinflip plugin;

    public EscrowManager(AriesCoinflip plugin) {
        this.plugin = plugin;
    }

    public void addToEscrow(Player player, double amount) {
        addToEscrow(player.getUniqueId(), amount);
    }

    public void addToEscrow(UUID playerUUID, double amount) {
        // Here need to withdraw player
        escrowBalances.put(playerUUID, escrowBalances.getOrDefault(playerUUID, 0.0) + amount);
    }

    public void removeFromEscrow(Player player, double amount) {
        removeFromEscrow(player.getUniqueId(), amount);
    }

    public void removeFromEscrow(UUID playerUUID, double amount) {
        escrowBalances.put(playerUUID, escrowBalances.getOrDefault(playerUUID, 0.0) - amount);
        // Here need to deposit player
    }

    /**
     * Set the escrow balance for a player
     * 
     * @param playerUUID Player UUID
     * @param balance Escrow balance
     */
    public void setBalance(UUID playerUUID, double balance) {
        escrowBalances.put(playerUUID, balance);
    }

    /**
     * Get the escrow balance for a player
     * 
     * @param playerUUID Player UUID
     * @return Escrow balance
     */
    public double getBalance(UUID playerUUID) {
        return escrowBalances.getOrDefault(playerUUID, 0.0);
    }

    /**
     * Get all escrow balances
     * 
     * @return Map of player UUIDs to escrow balances
     */
    public Map<UUID, Double> getAllBalances() {
        return new HashMap<>(escrowBalances);
    }
}
