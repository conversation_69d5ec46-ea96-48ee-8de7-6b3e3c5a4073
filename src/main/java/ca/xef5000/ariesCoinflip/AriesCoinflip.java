package ca.xef5000.ariesCoinflip;

import ca.xef5000.ariesCoinflip.commands.CoinflipCommand;
import ca.xef5000.ariesCoinflip.commands.CoinflipTabCompleter;
import ca.xef5000.ariesCoinflip.listeners.InventoryListener;
import ca.xef5000.ariesCoinflip.listeners.PlayerDisconnectListener;
import ca.xef5000.ariesCoinflip.managers.*;
import ca.xef5000.ariesCoinflip.models.currencies.CurrencyIntegrations;
import ca.xef5000.ariesCoinflip.models.currencies.VaultCurrencyIntegration;
import ca.xef5000.ariesCoinflip.placeholders.CoinflipPlaceholders;
import ca.xef5000.ariesCoinflip.utils.ConfigManager;
import org.bukkit.Bukkit;
import org.bukkit.plugin.java.JavaPlugin;
import org.bukkit.scheduler.BukkitTask;

public final class AriesCoinflip extends JavaPlugin {
    private ConfigManager configManager;
    private DataManager dataManager;
    private GUIManager guiManager;
    private CoinflipManager coinflipManager;
    private EscrowManager escrowManager;
    private ColorGUIManager colorGUIManager;
    private AnimationManager animationManager;
    private GameManager gameManager;
    private CurrencyManager currencyManager;
    private BukkitTask updateTask;



    @Override
    public void onEnable() {
        // Initialize managers
        configManager = new ConfigManager(this);
        currencyManager = new CurrencyManager(this, configManager);
        escrowManager = new EscrowManager(this);
        dataManager = new DataManager(this);
        gameManager = new GameManager(this);
        guiManager = new GUIManager(this, configManager, dataManager);
        colorGUIManager = new ColorGUIManager(this, configManager, dataManager);
        animationManager = new AnimationManager(this, configManager, dataManager);
        coinflipManager = new CoinflipManager(this, configManager, dataManager);
        currencyManager = new CurrencyManager(this, configManager);

        // Register command
        getCommand("coinflip").setExecutor(new CoinflipCommand(this, configManager, guiManager));
        getCommand("coinflip").setTabCompleter(new CoinflipTabCompleter());

        // Register listeners
        Bukkit.getPluginManager().registerEvents(new InventoryListener(this, configManager, dataManager, guiManager), this);
        Bukkit.getPluginManager().registerEvents(new PlayerDisconnectListener(this), this);

        // Start GUI update task (updates every second)
        updateTask = Bukkit.getScheduler().runTaskTimer(this, guiManager::updateOpenInventories, 20L, 20L);

        // Register PlaceholderAPI expansion if available
        if (Bukkit.getPluginManager().getPlugin("PlaceholderAPI") != null) {
            getLogger().info("PlaceholderAPI found! Registering placeholders...");
            new CoinflipPlaceholders(this).register();
        }

        getLogger().info("AriesCoinflip has been enabled!");
    }

    @Override
    public void onDisable() {
        // Clean up animations
        if (animationManager != null) {
            animationManager.cleanup();
        }

        // Clean up games
        if (gameManager != null) {
            gameManager.cleanup();
        }

        // Save data
        if (dataManager != null) {
            dataManager.saveData();
            dataManager.closeDatabase();
        }

        // Cancel tasks
        if (updateTask != null) {
            updateTask.cancel();
        }

        getLogger().info("AriesCoinflip has been disabled!");
    }

    public ConfigManager getConfigManager() {
        return configManager;
    }

    public DataManager getDataManager() {
        return dataManager;
    }

    public GUIManager getGuiManager() {
        return guiManager;
    }

    public CoinflipManager getCoinflipManager() {
        return coinflipManager;
    }

    public EscrowManager getEscrowManager() {
        return escrowManager;
    }

    public ColorGUIManager getColorGUIManager() {
        return colorGUIManager;
    }

    public AnimationManager getAnimationManager() {
        return animationManager;
    }

    public GameManager getGameManager() {
        return gameManager;
    }

    public CurrencyManager getCurrencyManager() {
        return currencyManager;
    }
}
