# AriesCoinflip Configuration

# Main GUI Configuration
gui:
  title: "&6&lCoinflip"
  rows: 6

  # Static Items Configuration
  items:
    # Statistics Item (Top Right Corner)
    statistics:
      enabled: true
      slot: 8
      material: PLAYER_HEAD
      name: "&a&lYour Statistics"
      lore:
        - "&7Click to view your statistics"
        - "&7"
        - "&7Games Played: &e{games_played}"
        - "&7Games Won: &a{games_won}"
        - "&7Games Lost: &c{games_lost}"
        - "&7Win Rate: &e{win_rate}%"

    # Close Button (Bottom Left Corner)
    close:
      enabled: true
      slot: 45
      material: BARRIER
      name: "&c&lClose Menu"
      lore:
        - "&7Click to close the menu"

    # Coinflip Offer Item (Used for displaying coinflip offers)
    offer-item:
      enabled: true
      material: PLAYER_HEAD
      name: "&e{player}'s Coinflip"
      lore:
        - "&7Amount: &a${amount}"
        - "&7Color: {color}"
        - "&7Time remaining: &e{time_remaining}"
        - "&7"
        - "&aClick to accept"
      # Slots where coinflip offers will be displayed (rows 2-5)
      slots: [9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44]

  # Decorative Items
  decorative:
    # Tutorial Item (Slot 0)
    tutorial:
      enabled: true
      slot: 0
      material: BOOK
      name: "&6&lCreate a Coinflip"
      lore:
        - "&f| To create a coinflip, use the command"
        - "&f| /&6coinflip create &f[&amoney&f] [&6amount&f]"
        - "&r"
        - "&f| The minimum bet amount are"
        - "&f| &aMoney: $1000"
        - "&7To accept a coinflip:"
        - "&7- Click on a player's head"
        - "&7- Confirm your bet"
        - "&7"
        - "&7Good luck!"

    # Information Item
    info:
      enabled: true
      slot: 4
      material: PAPER
      name: "&b&lCoinflip"
      lore:
        - "&7| Coinflip for money!"
        - "&7| Minimum bet: $1000"
        - "&7| Maximum bet: $1,000,000"
        - "&7| Tax: 5%"
        - "&7| Timeout: 5 minutes"
        - "&7| To create a coinflip, use the command"
        - "&7| /&6coinflip create &f[&amoney&f] [&6amount&f]"

    # Example decorative item 1
    item1:
      enabled: true
      slot: 1
      material: GOLD_INGOT
      name: "&6&lGood luck!"
      lore:
        - "&r"

    # Border items (top and bottom rows)
    border:
      enabled: true
      slots: [2, 3, 5, 6, 7, 46, 47, 48, 49, 50, 51, 52, 53]
      material: BLACK_STAINED_GLASS_PANE
      name: " "
      lore: []

# Coinflip Settings
settings:
  # Minimum bet amount
  min_bet: 1000

  # Maximum bet amount
  max_bet: 1000000

  # Percentage of tax to take from the winner
  tax_percentage: 5

  # Time in seconds before a coinflip offer expires
  offer_timeout: 300

  # Default luck value for all players
  # This is the base luck value that all players start with
  # Players can gain additional luck through the /cf luck command
  # Higher luck values give players a better chance of winning coinflips
  default_luck: 100

  public_announcements: true

  # Animation settings
  animations:
    # Enable/disable animations
    enabled: true

    # Rolling animation duration in ticks (20 ticks = 1 second)
    rolling_duration: 100

    # Winner animation duration in ticks
    winner_duration: 120

    # Auto-close winner GUI after animation (in ticks, 0 = don't auto-close)
    auto_close_delay: 60

    # Whether to cancel the bet when a player closes the rolling animation
    # When true, the bet is cancelled and the other player's GUI is closed
    # When false, the game continues even if players close the GUI
    cancel_bet_on_animation_cancel: true

  # Sound effects
  sounds:
    open_menu: BLOCK_CHEST_OPEN
    close_menu: BLOCK_CHEST_CLOSE
    create_coinflip: ENTITY_EXPERIENCE_ORB_PICKUP
    accept_coinflip: ENTITY_PLAYER_LEVELUP
    win_coinflip: ENTITY_PLAYER_LEVELUP
    lose_coinflip: ENTITY_VILLAGER_NO

currencies:
  money:
    symbol: "$"
    min_bet: 1000
    max_bet: 1000000
    integration: Vault

# Database Configuration
database:
  # Database type: sqlite or mysql
  type: "sqlite"

  # MySQL Configuration (only used if type is mysql)
  mysql:
    host: "localhost"
    port: 3306
    database: "coinflip"
    username: "root"
    password: "password"

# Messages
messages:
  prefix: "&6[&eCoinflip&6] "
  no_permission: "&cYou don't have permission to use this command."
  no_permission_create: "&cYou don't have permission to create coinflips."
  no_permission_reload: "&cYou don't have permission to reload the config."
  config_reloaded: "&aConfiguration reloaded successfully."
  created_coinflip: "&aYou created a coinflip for &e${amount}."
  accepted_coinflip: "&aYou accepted &e{player}'s &acoinflip for &e${amount}."
  won_coinflip: "&aYou won &e${amount} &afrom &e{player}!"
  lost_coinflip: "&cYou lost &e${amount} &cto &e{player}."
  insufficient_funds: "&cYou don't have enough money."
  minimum_bet: "&cThe minimum bet is &e${min_bet}."
  maximum_bet: "&cThe maximum bet is &e${max_bet}."
  coinflip_expired: "&cYour coinflip offer has expired."
  coinflip_cancelled: "&aYour coinflip offer has been cancelled."
  coinflip_bet_cancelled: "&cCoinflip cancelled. Your bet has been returned."
  coinflip_other_player_left: "&cThe other player left the game. Your bet has been returned."
  no_permission_luck: "&cYou don't have permission to use luck commands."
  no_permission_luck_admin: "&cYou don't have permission to modify other players' luck."
  luck_get_self: "&aYour luck: &e{luck}"
  luck_get_other: "&a{player}'s luck: &e{luck}"
  luck_add: "&aAdded &e{amount} &aluck to &e{player}&a. New luck: &e{luck}"
  luck_remove: "&aRemoved &e{amount} &aluck from &e{player}&a. New luck: &e{luck}"
  luck_set: "&aSet &e{player}&a's luck to &e{amount}"
  luck_update_all: "&aUpdated luck for &e{count} &aplayers."
  public_announcements: "&c{loser} &alost &6{amount} &aagainst &c{winner} &ain a coinflip!"
