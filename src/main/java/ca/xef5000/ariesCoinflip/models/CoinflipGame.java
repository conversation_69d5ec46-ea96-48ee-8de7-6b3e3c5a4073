package ca.xef5000.ariesCoinflip.models;

import ca.xef5000.ariesCoinflip.AriesCoinflip;
import ca.xef5000.ariesCoinflip.managers.EscrowManager;
import ca.xef5000.ariesCoinflip.utils.ConfigManager;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;

import java.util.Random;
import java.util.UUID;

/**
 * Represents a coinflip game with its own lifecycle and state management.
 * This class separates the game logic from the UI, allowing the game to
 * continue even if players close their GUIs.
 */
public class CoinflipGame {
    // Game states
    public enum CoinflipStatus {
        PENDING,    // Game created but not started
        ROLLING,    // Game is in progress (animation playing)
        COMPLETED,  // Game has completed and results processed
        CANCELLED   // Game was cancelled (e.g., player disconnected)
    }

    private final UUID gameId;
    private final UUID creatorUUID;
    private final String creatorName;
    private final UUID accepterUUID;
    private final String accepterName;
    private final double amount;
    private final CoinflipColor creatorColor;
    private final CoinflipColor accepterColor;
    private final String currency;
    private final long startTime;
    private final boolean creatorWins;
    private CoinflipStatus status;
    private final AriesCoinflip plugin;
    private boolean resultsProcessed;
    private final int creatorLuck;
    private final int accepterLuck;

    /**
     * Creates a new coinflip game
     * 
     * @param creator The player who created the coinflip
     * @param accepter The player who accepted the coinflip
     * @param amount The bet amount
     * @param creatorColor The color selected by the creator
     * @param accepterColor The color selected by the accepter
     * @param plugin Reference to the main plugin
     */
    public CoinflipGame(Player creator, Player accepter, double amount, 
                       CoinflipColor creatorColor, CoinflipColor accepterColor, AriesCoinflip plugin, boolean forceWin) {
        this.gameId = UUID.randomUUID();
        this.creatorUUID = creator.getUniqueId();
        this.creatorName = creator.getName();
        this.accepterUUID = accepter.getUniqueId();
        this.accepterName = accepter.getName();
        this.amount = amount;
        this.creatorColor = creatorColor;
        this.accepterColor = accepterColor;
        this.startTime = System.currentTimeMillis();
        this.plugin = plugin;
        this.status = CoinflipStatus.PENDING;
        this.resultsProcessed = false;

        // Get luck values from PlayerStats
        PlayerStats creatorStats = plugin.getDataManager().getPlayerStats(creator);
        PlayerStats accepterStats = plugin.getDataManager().getPlayerStats(accepter);
        this.creatorLuck = creatorStats.getLuck();
        this.accepterLuck = accepterStats.getLuck();

        if (forceWin) {
            this.creatorWins = false;
        } else {
            // Determine winner based on luck
            int defaultLuck = plugin.getConfigManager().getConfig().getInt("settings.default_luck", 100);
            int creatorTotalLuck = defaultLuck + creatorLuck;
            int accepterTotalLuck = defaultLuck + accepterLuck;
            int totalLuck = creatorTotalLuck + accepterTotalLuck;

            // Calculate probability based on luck
            double creatorProbability = (double) creatorTotalLuck / totalLuck;

            // Determine winner
            Random random = new Random();
            this.creatorWins = random.nextDouble() < creatorProbability;
        }
    }

    /**
     * Start the game (transition from PENDING to ROLLING)
     */
    public void startGame() {
        if (status == CoinflipStatus.PENDING) {
            status = CoinflipStatus.ROLLING;
        }
    }

    /**
     * Process the game results (transfer money, update stats)
     * This can be called independently of the UI state
     * 
     * @return true if results were processed, false if already processed
     */
    public boolean processResults() {
        if (resultsProcessed) {
            return false; // Already processed
        }

        // Update status
        status = CoinflipStatus.COMPLETED;
        resultsProcessed = true;

        // Update stats
        PlayerStats creatorStats = plugin.getDataManager().getPlayerStats(creatorUUID, creatorName);
        PlayerStats accepterStats = plugin.getDataManager().getPlayerStats(accepterUUID, accepterName);

        double betAmount = amount;
        double tax = betAmount * plugin.getConfigManager().getTaxPercentage() / 100;
        double winAmount = (betAmount * 2) - tax;

        // Get escrow manager
        EscrowManager escrowManager = plugin.getEscrowManager();

        if (creatorWins) {
            // Creator wins
            creatorStats.addWin(betAmount);
            accepterStats.addLoss(betAmount);

            // Both bets are already in escrow, give winnings to creator
            escrowManager.removeFromEscrow(creatorUUID, winAmount);

            // Send messages to online players
            Player creator = Bukkit.getPlayer(creatorUUID);
            Player accepter = Bukkit.getPlayer(accepterUUID);

            if (creator != null && creator.isOnline()) {
                creator.sendMessage(plugin.getConfigManager().getMessage("won_coinflip")
                        .replace("{amount}", String.format("%.2f", winAmount))
                        .replace("{player}", accepterName));
            }

            if (accepter != null && accepter.isOnline()) {
                accepter.sendMessage(plugin.getConfigManager().getMessage("lost_coinflip")
                        .replace("{amount}", String.format("%.2f", betAmount))
                        .replace("{player}", creatorName));
            }

            if (plugin.getConfigManager().isPublicAnnouncementsEnabled()) {
                Bukkit.broadcastMessage(plugin.getConfigManager().getMessage("public_announcements")
                        .replace("{winner}", creatorName)
                        .replace("{loser}", accepterName)
                        .replace("{amount}", String.format("%.2f", betAmount)));
            }
        } else {
            // Accepter wins
            accepterStats.addWin(betAmount);
            creatorStats.addLoss(betAmount);

            // Both bets are already in escrow, give winnings to accepter
            escrowManager.removeFromEscrow(accepterUUID, winAmount);

            // Send messages to online players
            Player creator = Bukkit.getPlayer(creatorUUID);
            Player accepter = Bukkit.getPlayer(accepterUUID);

            if (accepter != null && accepter.isOnline()) {
                accepter.sendMessage(plugin.getConfigManager().getMessage("won_coinflip")
                        .replace("{amount}", String.format("%.2f", winAmount))
                        .replace("{player}", creatorName));
            }

            if (creator != null && creator.isOnline()) {
                creator.sendMessage(plugin.getConfigManager().getMessage("lost_coinflip")
                        .replace("{amount}", String.format("%.2f", betAmount))
                        .replace("{player}", accepterName));
            }

            if (plugin.getConfigManager().isPublicAnnouncementsEnabled()) {
                Bukkit.broadcastMessage(plugin.getConfigManager().getMessage("public_announcements")
                        .replace("{winner}", accepterName)
                        .replace("{loser}", creatorName)
                        .replace("{amount}", String.format("%.2f", betAmount)));
            }
        }

        // Save updated stats
        plugin.getDataManager().saveData();

        return true;
    }

    /**
     * Cancel the game and refund both players
     */
    public void cancelGame() {
        if (status == CoinflipStatus.COMPLETED) {
            return; // Don't cancel completed games
        }

        status = CoinflipStatus.CANCELLED;

        if (!resultsProcessed) {
            // Refund both players
            EscrowManager escrowManager = plugin.getEscrowManager();

            // Return creator's bet if they're online
            Player creator = Bukkit.getPlayer(creatorUUID);
            escrowManager.removeFromEscrow(creatorUUID, amount);
            if (creator != null && creator.isOnline()) {
                creator.sendMessage(ConfigManager.colorize("&cCoinflip cancelled. Your bet has been returned."));
            }

            // Return accepter's bet if they're online
            Player accepter = Bukkit.getPlayer(accepterUUID);
            escrowManager.removeFromEscrow(accepterUUID, amount);
            if (accepter != null && accepter.isOnline()) {
                accepter.sendMessage(ConfigManager.colorize("&cCoinflip cancelled. Your bet has been returned."));
            }

            resultsProcessed = true;
        }
    }



    /**
     * Get the game's unique ID
     */
    public UUID getGameId() {
        return gameId;
    }

    /**
     * Get the creator's UUID
     */
    public UUID getCreatorUUID() {
        return creatorUUID;
    }

    /**
     * Get the creator's name
     */
    public String getCreatorName() {
        return creatorName;
    }

    /**
     * Get the accepter's UUID
     */
    public UUID getAccepterUUID() {
        return accepterUUID;
    }

    /**
     * Get the accepter's name
     */
    public String getAccepterName() {
        return accepterName;
    }

    /**
     * Get the bet amount
     */
    public double getAmount() {
        return amount;
    }

    /**
     * Get the creator's selected color
     */
    public CoinflipColor getCreatorColor() {
        return creatorColor;
    }

    /**
     * Get the accepter's selected color
     */
    public CoinflipColor getAccepterColor() {
        return accepterColor;
    }

    /**
     * Get the start time of this coinflip
     */
    public long getStartTime() {
        return startTime;
    }

    /**
     * Check if the creator wins this coinflip
     */
    public boolean doesCreatorWin() {
        return creatorWins;
    }

    /**
     * Get the winner's UUID
     */
    public UUID getWinnerUUID() {
        return creatorWins ? creatorUUID : accepterUUID;
    }

    /**
     * Get the winner's name
     */
    public String getWinnerName() {
        return creatorWins ? creatorName : accepterName;
    }

    /**
     * Get the loser's UUID
     */
    public UUID getLoserUUID() {
        return creatorWins ? accepterUUID : creatorUUID;
    }

    /**
     * Get the loser's name
     */
    public String getLoserName() {
        return creatorWins ? accepterName : creatorName;
    }

    /**
     * Get the winner's color
     */
    public CoinflipColor getWinnerColor() {
        return creatorWins ? creatorColor : accepterColor;
    }

    /**
     * Get the loser's color
     */
    public CoinflipColor getLoserColor() {
        return creatorWins ? accepterColor : creatorColor;
    }

    /**
     * Get the current status of the game
     */
    public CoinflipStatus getStatus() {
        return status;
    }

    /**
     * Check if a player is involved in this coinflip
     */
    public boolean isPlayerInvolved(UUID playerUUID) {
        return creatorUUID.equals(playerUUID) || accepterUUID.equals(playerUUID);
    }

    /**
     * Check if the game results have been processed
     */
    public boolean areResultsProcessed() {
        return resultsProcessed;
    }
}
