package ca.xef5000.ariesCoinflip.managers;

import ca.xef5000.ariesCoinflip.AriesCoinflip;
import ca.xef5000.ariesCoinflip.models.CoinflipColor;
import ca.xef5000.ariesCoinflip.models.CoinflipOffer;
import ca.xef5000.ariesCoinflip.utils.ConfigManager;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.inventory.meta.SkullMeta;

import java.io.File;
import java.util.*;

/**
 * Manages color selection GUIs for coinflip creation and acceptance
 */
public class ColorGUIManager {
    private final AriesCoinflip plugin;
    private final ConfigManager configManager;
    private final DataManager dataManager;
    private FileConfiguration colorConfig;

    // Track players in color selection process
    private final Map<UUID, Double> creatingCoinflips = new HashMap<>(); // UUID -> bet amount
    private final Map<UUID, String> creatingCoinflipsCurrency = new HashMap<>(); // UUID -> currency
    private final Map<UUID, CoinflipOffer> acceptingCoinflips = new HashMap<>(); // UUID -> offer being accepted
    private final Map<UUID, CoinflipColor> selectedColors = new HashMap<>(); // UUID -> selected color

    public ColorGUIManager(AriesCoinflip plugin, ConfigManager configManager, DataManager dataManager) {
        this.plugin = plugin;
        this.configManager = configManager;
        this.dataManager = dataManager;
        loadColorConfig();
    }

    /**
     * Load the color GUI configuration
     */
    private void loadColorConfig() {
        File configFile = new File(plugin.getDataFolder(), "color_gui.yml");
        if (!configFile.exists()) {
            plugin.saveResource("color_gui.yml", false);
        }
        colorConfig = YamlConfiguration.loadConfiguration(configFile);
    }

    /**
     * Reload the color GUI configuration
     */
    public void reloadConfig() {
        loadColorConfig();
    }

    /**
     * Open color selection GUI for creating a coinflip
     * @param player The player creating the coinflip
     * @param betAmount The bet amount
     * @param currency The currency being used
     */
    public void openCreatorColorGUI(Player player, double betAmount, String currency) {
        creatingCoinflips.put(player.getUniqueId(), betAmount);
        creatingCoinflipsCurrency.put(player.getUniqueId(), currency);
        selectedColors.remove(player.getUniqueId()); // Clear any previous selection

        String title = ConfigManager.colorize(colorConfig.getString("gui.title", "&6&lSelect Your Color"));
        int rows = colorConfig.getInt("gui.rows", 3);
        Inventory inventory = Bukkit.createInventory(null, rows * 9, title);

        // Add color options (no creator color to mark as chosen)
        addColorOptions(inventory, null, null);

        // Add decorative items
        addDecorativeItems(inventory);

        player.openInventory(inventory);
    }

    /**
     * Legacy method for backward compatibility (uses default currency)
     */
    @Deprecated
    public void openCreatorColorGUI(Player player, double betAmount) {
        openCreatorColorGUI(player, betAmount, "money");
    }

    /**
     * Open color selection GUI for accepting a coinflip
     * @param player The player accepting the coinflip
     * @param offer The coinflip offer being accepted
     */
    public void openAccepterColorGUI(Player player, CoinflipOffer offer) {
        acceptingCoinflips.put(player.getUniqueId(), offer);
        selectedColors.remove(player.getUniqueId()); // Clear any previous selection

        String title = ConfigManager.colorize(colorConfig.getString("gui.title", "&6&lSelect Your Color"));
        int rows = colorConfig.getInt("gui.rows", 3);
        Inventory inventory = Bukkit.createInventory(null, rows * 9, title);

        // Add color options with creator's color marked as chosen
        addColorOptions(inventory, offer.getSelectedColor(), offer.getPlayerName());

        // Add decorative items
        addDecorativeItems(inventory);

        // Add creator's color display
        addCreatorColorDisplay(inventory, offer);

        player.openInventory(inventory);
    }

    /**
     * Handle color selection by a player
     * @param player The player selecting a color
     * @param selectedColor The color they selected
     */
    public void handleColorSelection(Player player, CoinflipColor selectedColor) {
        UUID playerUUID = player.getUniqueId();
        selectedColors.put(playerUUID, selectedColor);

        if (creatingCoinflips.containsKey(playerUUID)) {
            // Player is creating a coinflip
            plugin.getLogger().info("Player " + player.getName() + " selected color " + selectedColor + " for creating coinflip");
            handleCreatorColorSelection(player, selectedColor);
        } else if (acceptingCoinflips.containsKey(playerUUID)) {
            // Player is accepting a coinflip
            plugin.getLogger().info("Player " + player.getName() + " selected color " + selectedColor + " for accepting coinflip");
            handleAccepterColorSelection(player, selectedColor);
        } else {
            plugin.getLogger().warning("Player " + player.getName() + " selected color " + selectedColor + " but is not in color selection process");
        }
    }

    /**
     * Handle color selection for coinflip creation
     */
    private void handleCreatorColorSelection(Player player, CoinflipColor selectedColor) {
        UUID playerUUID = player.getUniqueId();
        Double betAmount = creatingCoinflips.get(playerUUID);
        String currency = creatingCoinflipsCurrency.get(playerUUID);

        if (betAmount == null || currency == null) {
            player.closeInventory();
            return;
        }

        // Get currency object and check if player still has enough money
        ca.xef5000.ariesCoinflip.models.currencies.Currency currencyObj = plugin.getCurrencyManager().getCurrency(currency);
        if (currencyObj == null || currencyObj.getIntegration().getBalance(player) < betAmount) {
            player.sendMessage(configManager.getMessage("insufficient_funds"));
            player.closeInventory();
            creatingCoinflips.remove(playerUUID);
            creatingCoinflipsCurrency.remove(playerUUID);
            selectedColors.remove(playerUUID);
            return;
        }

        // Update the GUI to show confirmation button
        updateCreatorGUIWithConfirmation(player, selectedColor);
    }

    /**
     * Update the creator GUI to show the confirmation button
     */
    private void updateCreatorGUIWithConfirmation(Player player, CoinflipColor selectedColor) {
        Inventory inventory = player.getOpenInventory().getTopInventory();

        // Add confirmation button
        ConfigurationSection confirmSection = colorConfig.getConfigurationSection("creator_items.confirm");
        if (confirmSection != null) {
            int slot = confirmSection.getInt("slot", 22);
            String materialName = confirmSection.getString("material", "GREEN_GLAZED_TERRACOTTA");
            String name = confirmSection.getString("name", "&a&lCreate Coinflip!");
            List<String> lore = confirmSection.getStringList("lore");

            Material material;
            try {
                material = Material.valueOf(materialName.toUpperCase());
            } catch (IllegalArgumentException e) {
                material = Material.GREEN_GLAZED_TERRACOTTA;
            }

            ItemStack confirmButton = new ItemStack(material);
            ItemMeta meta = confirmButton.getItemMeta();
            meta.setDisplayName(ConfigManager.colorize(name));
            meta.setLore(ConfigManager.colorizeList(lore));
            confirmButton.setItemMeta(meta);

            inventory.setItem(slot, confirmButton);
        }
    }

    /**
     * Handle confirmation of coinflip creation
     */
    public void handleCreatorConfirmation(Player player) {
        UUID playerUUID = player.getUniqueId();
        Double betAmount = creatingCoinflips.get(playerUUID);
        String currency = creatingCoinflipsCurrency.get(playerUUID);
        CoinflipColor selectedColor = selectedColors.get(playerUUID);

        if (betAmount == null || currency == null || selectedColor == null) {
            player.closeInventory();
            return;
        }

        // Get currency object and check if player still has enough money
        ca.xef5000.ariesCoinflip.models.currencies.Currency currencyObj = plugin.getCurrencyManager().getCurrency(currency);
        if (currencyObj == null || currencyObj.getIntegration().getBalance(player) < betAmount) {
            player.sendMessage(configManager.getMessage("insufficient_funds"));
            player.closeInventory();
            creatingCoinflips.remove(playerUUID);
            creatingCoinflipsCurrency.remove(playerUUID);
            selectedColors.remove(playerUUID);
            return;
        }

        // Withdraw money from player and put in escrow
        plugin.getEscrowManager().addToEscrow(player, betAmount, currency);

        // Create the coinflip offer with color and currency
        dataManager.addCoinflipOffer(player, betAmount, configManager.getOfferTimeout(), selectedColor, currency);

        // Send confirmation message
        player.sendMessage(configManager.getMessage("created_coinflip")
                .replace("{amount}", String.format("%.2f", betAmount))
                .replace("{color}", selectedColor.getColoredDisplayName()));

        // Clean up
        creatingCoinflips.remove(playerUUID);
        creatingCoinflipsCurrency.remove(playerUUID);
        selectedColors.remove(playerUUID);
        player.closeInventory();
    }

    /**
     * Handle color selection for coinflip acceptance
     */
    private void handleAccepterColorSelection(Player player, CoinflipColor selectedColor) {
        UUID playerUUID = player.getUniqueId();
        CoinflipOffer offer = acceptingCoinflips.get(playerUUID);

        if (offer == null) {
            player.closeInventory();
            return;
        }

        // Check if the selected color is the same as the creator's color
        if (selectedColor == offer.getSelectedColor()) {
            player.sendMessage(ConfigManager.colorize("&cYou cannot select the same color as the creator."));
            return;
        }

        // Update the GUI to show confirmation button
        updateAccepterGUIWithConfirmation(player, offer, selectedColor);
    }

    /**
     * Update the accepter GUI to show the confirmation button
     */
    private void updateAccepterGUIWithConfirmation(Player player, CoinflipOffer offer, CoinflipColor selectedColor) {
        Inventory inventory = player.getOpenInventory().getTopInventory();

        // Add confirmation button
        ConfigurationSection confirmSection = colorConfig.getConfigurationSection("accepter_items.confirm");
        if (confirmSection != null) {
            int slot = confirmSection.getInt("slot", 22);
            String materialName = confirmSection.getString("material", "GREEN_WOOL");
            String name = confirmSection.getString("name", "&a&lStart Coinflip!");
            List<String> lore = confirmSection.getStringList("lore");

            Material material;
            try {
                material = Material.valueOf(materialName.toUpperCase());
            } catch (IllegalArgumentException e) {
                material = Material.GREEN_WOOL;
            }

            ItemStack confirmButton = new ItemStack(material);
            ItemMeta meta = confirmButton.getItemMeta();
            meta.setDisplayName(ConfigManager.colorize(name));
            meta.setLore(ConfigManager.colorizeList(lore));
            confirmButton.setItemMeta(meta);

            inventory.setItem(slot, confirmButton);
        }
    }

    /**
     * Handle confirmation of coinflip acceptance
     */
    public void handleAcceptanceConfirmation(Player player, boolean forceWin) {
        UUID playerUUID = player.getUniqueId();
        CoinflipOffer offer = acceptingCoinflips.get(playerUUID);
        CoinflipColor accepterColor = selectedColors.get(playerUUID);

        if (offer == null || accepterColor == null) {
            player.closeInventory();
            return;
        }

        // Check if offer is still valid
        if (offer.isExpired() || !dataManager.hasActiveOffer(offer.getPlayerUUID())) {
            player.sendMessage(ConfigManager.colorize("&cThis coinflip offer is no longer available."));
            player.closeInventory();
            cleanupPlayer(playerUUID);
            return;
        }

        // Check if accepting player has enough money in the correct currency
        double betAmount = offer.getAmount();
        String currency = offer.getCurrency();
        ca.xef5000.ariesCoinflip.models.currencies.Currency currencyObj = plugin.getCurrencyManager().getCurrency(currency);

        if (currencyObj == null || currencyObj.getIntegration().getBalance(player) < betAmount) {
            player.sendMessage(configManager.getMessage("insufficient_funds"));
            player.closeInventory();
            cleanupPlayer(playerUUID);
            return;
        }

        // Get the creator player
        Player creator = Bukkit.getPlayer(offer.getPlayerUUID());
        if (creator == null || !creator.isOnline()) {
            player.sendMessage(ConfigManager.colorize("&cThe other player is no longer online."));
            player.closeInventory();
            cleanupPlayer(playerUUID);
            return;
        }

        // Add accepter's bet to escrow
        plugin.getEscrowManager().addToEscrow(player, betAmount, currency);

        // Start the coinflip animation
        plugin.getAnimationManager().startCoinflipAnimation(creator, player, offer, accepterColor, forceWin);

        // Clean up
        cleanupPlayer(playerUUID);
        //player.closeInventory();
    }

    /**
     * Add color options to the inventory
     * @param inventory The inventory to add color options to
     * @param creatorColor The creator's selected color (null if this is for the creator)
     * @param creatorName The creator's name (null if this is for the creator)
     */
    private void addColorOptions(Inventory inventory, CoinflipColor creatorColor, String creatorName) {
        ConfigurationSection colorsSection = colorConfig.getConfigurationSection("colors");
        if (colorsSection == null) return;

        for (String colorKey : colorsSection.getKeys(false)) {
            ConfigurationSection colorSection = colorsSection.getConfigurationSection(colorKey);
            if (colorSection == null || !colorSection.getBoolean("enabled", true)) continue;

            CoinflipColor color = CoinflipColor.fromConfigKey(colorKey);
            if (color == null) continue;

            int slot = colorSection.getInt("slot", 0);
            String materialName = colorSection.getString("material", color.getWoolMaterial().name());
            String name = colorSection.getString("name", color.getColoredDisplayName());
            List<String> lore = new ArrayList<>(colorSection.getStringList("lore"));

            // If this is the accepter's GUI and this color is the creator's color, add the "color chosen" lore
            if (creatorColor != null && color == creatorColor) {
                List<String> chosenLore = colorConfig.getStringList("accepter_items.color_chosen_lore");
                for (String line : chosenLore) {
                    lore.add(ConfigManager.colorize(line.replace("{creator_name}", creatorName)));
                }
            }

            Material material;
            try {
                material = Material.valueOf(materialName.toUpperCase());
            } catch (IllegalArgumentException e) {
                material = color.getWoolMaterial();
            }

            ItemStack item = new ItemStack(material);
            ItemMeta meta = item.getItemMeta();
            meta.setDisplayName(ConfigManager.colorize(name));
            meta.setLore(ConfigManager.colorizeList(lore));
            item.setItemMeta(meta);

            inventory.setItem(slot, item);
        }
    }

    /**
     * Add color options to the inventory (overloaded method for backward compatibility)
     */
    private void addColorOptions(Inventory inventory) {
        addColorOptions(inventory, null, null);
    }

    /**
     * Add decorative items to the inventory
     */
    private void addDecorativeItems(Inventory inventory) {
        ConfigurationSection decorativeSection = colorConfig.getConfigurationSection("decorative");
        if (decorativeSection == null) return;

        for (String key : decorativeSection.getKeys(false)) {
            ConfigurationSection itemSection = decorativeSection.getConfigurationSection(key);
            if (itemSection == null || !itemSection.getBoolean("enabled", true)) continue;

            String materialName = itemSection.getString("material", "BLACK_STAINED_GLASS_PANE");
            String name = itemSection.getString("name", " ");
            List<String> lore = itemSection.getStringList("lore");
            List<Integer> slots = itemSection.getIntegerList("slots");

            Material material;
            try {
                material = Material.valueOf(materialName.toUpperCase());
            } catch (IllegalArgumentException e) {
                material = Material.BLACK_STAINED_GLASS_PANE;
            }

            ItemStack item = new ItemStack(material);
            ItemMeta meta = item.getItemMeta();
            meta.setDisplayName(ConfigManager.colorize(name));
            meta.setLore(ConfigManager.colorizeList(lore));
            item.setItemMeta(meta);

            for (int slot : slots) {
                inventory.setItem(slot, item);
            }
        }
    }

    /**
     * Add creator's color display for accepter GUI
     */
    private void addCreatorColorDisplay(Inventory inventory, CoinflipOffer offer) {
        ConfigurationSection creatorColorSection = colorConfig.getConfigurationSection("accepter_items.creator_color");
        if (creatorColorSection == null) return;

        int slot = creatorColorSection.getInt("slot", 4);
        String name = creatorColorSection.getString("name", "&e{creator_name}'s Color");
        List<String> lore = creatorColorSection.getStringList("lore");

        // Create item showing creator's color
        ItemStack item = new ItemStack(offer.getSelectedColor().getWoolMaterial());
        ItemMeta meta = item.getItemMeta();

        meta.setDisplayName(ConfigManager.colorize(name
                .replace("{creator_name}", offer.getPlayerName())));

        List<String> processedLore = new ArrayList<>();
        for (String loreLine : lore) {
            processedLore.add(ConfigManager.colorize(loreLine
                    .replace("{creator_name}", offer.getPlayerName())
                    .replace("{color_name}", offer.getSelectedColor().getColoredDisplayName())));
        }
        meta.setLore(processedLore);

        item.setItemMeta(meta);
        inventory.setItem(slot, item);
    }

    /**
     * Check if a player is in color selection process
     */
    public boolean isPlayerInColorSelection(UUID playerUUID) {
        return creatingCoinflips.containsKey(playerUUID) || acceptingCoinflips.containsKey(playerUUID);
    }

    public boolean isPlayerCreatingCoinflip(UUID playerUUID) {
        return creatingCoinflips.containsKey(playerUUID);
    }

    /**
     * Clean up player data
     */
    public void cleanupPlayer(UUID playerUUID) {
        creatingCoinflips.remove(playerUUID);
        creatingCoinflipsCurrency.remove(playerUUID);
        acceptingCoinflips.remove(playerUUID);
        selectedColors.remove(playerUUID);
    }

    /**
     * Get the color configuration
     */
    public FileConfiguration getColorConfig() {
        return colorConfig;
    }
}
