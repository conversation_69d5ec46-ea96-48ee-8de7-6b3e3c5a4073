package ca.xef5000.ariesCoinflip.models.currencies;

public class Currency {
    private String name;
    private String symbol;
    private double minBet;
    private double maxBet;
    private CurrencyIntegration integration;

    public Currency(String name, String symbol, double minBet, double maxBet, CurrencyIntegration integration) {
        this.name = name;
        this.symbol = symbol;
        this.minBet = minBet;
        this.maxBet = maxBet;
        this.integration = integration;
    }

    public String getName() {
        return name;
    }

    public String getSymbol() {
        return symbol;
    }

    public double getMinBet() {
        return minBet;
    }

    public double getMaxBet() {
        return maxBet;
    }

    public CurrencyIntegration getIntegration() {
        return integration;
    }
}
