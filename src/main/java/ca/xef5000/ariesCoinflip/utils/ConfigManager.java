package ca.xef5000.ariesCoinflip.utils;

import ca.xef5000.ariesCoinflip.AriesCoinflip;
import org.bukkit.ChatColor;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;

import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

public class ConfigManager {
    private final AriesCoinflip plugin;
    private FileConfiguration config;
    private File configFile;

    public ConfigManager(AriesCoinflip plugin) {
        this.plugin = plugin;
        loadConfig();
    }

    public void loadConfig() {
        if (configFile == null) {
            configFile = new File(plugin.getDataFolder(), "config.yml");
        }

        if (!configFile.exists()) {
            plugin.saveResource("config.yml", false);
        }

        config = YamlConfiguration.loadConfiguration(configFile);
    }

    public void saveConfig() {
        if (config == null || configFile == null) {
            return;
        }

        try {
            config.save(configFile);
        } catch (IOException e) {
            plugin.getLogger().severe("Could not save config to " + configFile);
            e.printStackTrace();
        }
    }

    public FileConfiguration getConfig() {
        if (config == null) {
            loadConfig();
        }
        return config;
    }

    public String getGuiTitle() {
        return colorize(getConfig().getString("gui.title", "&6&lCoinflip"));
    }

    public int getGuiRows() {
        return getConfig().getInt("gui.rows", 6);
    }

    public double getMinBet() {
        return getConfig().getDouble("settings.min_bet", 1000);
    }

    public double getMaxBet() {
        return getConfig().getDouble("settings.max_bet", 1000000);
    }

    public double getTaxPercentage() {
        return getConfig().getDouble("settings.tax_percentage", 5);
    }

    public int getOfferTimeout() {
        return getConfig().getInt("settings.offer_timeout", 300);
    }

    public boolean isPublicAnnouncementsEnabled() {
        return getConfig().getBoolean("public_announcements", true);
    }

    public String getMessage(String path) {
        String prefix = getConfig().getString("messages.prefix", "&6[&eCoinflip&6] ");
        String message = getConfig().getString("messages." + path, "");
        return colorize(prefix + message);
    }

    public String getMessageWithoutPrefix(String path) {
        String message = getConfig().getString("messages." + path, "");
        return colorize(message);
    }

    public static String colorize(String message) {
        if (message == null) return "";
        return ChatColor.translateAlternateColorCodes('&', message);
    }

    public static List<String> colorizeList(List<String> list) {
        if (list == null) return List.of();
        return list.stream()
                .map(ConfigManager::colorize)
                .collect(Collectors.toList());
    }
}