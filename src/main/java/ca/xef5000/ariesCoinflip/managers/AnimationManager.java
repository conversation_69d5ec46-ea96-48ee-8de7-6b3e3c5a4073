package ca.xef5000.ariesCoinflip.managers;

import ca.xef5000.ariesCoinflip.AriesCoinflip;
import ca.xef5000.ariesCoinflip.models.ActiveCoinflip;
import ca.xef5000.ariesCoinflip.models.CoinflipColor;
import ca.xef5000.ariesCoinflip.models.CoinflipGame;
import ca.xef5000.ariesCoinflip.models.CoinflipOffer;
import ca.xef5000.ariesCoinflip.models.PlayerStats;
import ca.xef5000.ariesCoinflip.utils.ConfigManager;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.inventory.meta.SkullMeta;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;

import java.io.File;
import java.util.*;

/**
 * Manages coinflip animations including rolling and winner animations
 */
public class AnimationManager {
    private final AriesCoinflip plugin;
    private final ConfigManager configManager;
    private final DataManager dataManager;
    private FileConfiguration rollingConfig;
    private FileConfiguration winnerConfig;

    // Track active animations
    private final Map<UUID, BukkitTask> activeAnimations = new HashMap<>();
    private final Map<UUID, ActiveCoinflip> activeCoinflips = new HashMap<>();
    private final Set<UUID> processedCoinflips = new HashSet<>();
    private final Random random = new Random();

    public AnimationManager(AriesCoinflip plugin, ConfigManager configManager, DataManager dataManager) {
        this.plugin = plugin;
        this.configManager = configManager;
        this.dataManager = dataManager;
        loadConfigs();
    }

    /**
     * Load animation configurations
     */
    private void loadConfigs() {
        // Load rolling GUI config
        File rollingFile = new File(plugin.getDataFolder(), "rolling_gui.yml");
        if (!rollingFile.exists()) {
            plugin.saveResource("rolling_gui.yml", false);
        }
        rollingConfig = YamlConfiguration.loadConfiguration(rollingFile);

        // Load winner GUI config
        File winnerFile = new File(plugin.getDataFolder(), "winner_gui.yml");
        if (!winnerFile.exists()) {
            plugin.saveResource("winner_gui.yml", false);
        }
        winnerConfig = YamlConfiguration.loadConfiguration(winnerFile);
    }

    /**
     * Reload animation configurations
     */
    public void reloadConfigs() {
        loadConfigs();
    }

    /**
     * Start the coinflip animation for two players
     */
    public void startCoinflipAnimation(Player creator, Player accepter, CoinflipOffer offer, CoinflipColor accepterColor, boolean forceWin) {
        // Create a new game using the GameManager
        CoinflipGame game = plugin.getGameManager().startGameFromOffer(creator, accepter, offer, accepterColor, forceWin);

        // Start the game (transition to ROLLING state)
        game.startGame();

        // For backward compatibility, create an ActiveCoinflip object
        ActiveCoinflip coinflip = new ActiveCoinflip(creator, accepter, offer.getAmount(), 
                                                   offer.getSelectedColor(), accepterColor, game.doesCreatorWin());

        // Store the active coinflip for both players
        activeCoinflips.put(creator.getUniqueId(), coinflip);
        activeCoinflips.put(accepter.getUniqueId(), coinflip);

        // Start rolling animation for both players
        startRollingAnimation(creator, coinflip, game.getGameId());
        startRollingAnimation(accepter, coinflip, game.getGameId());
    }

    /**
     * Start the rolling animation for a player
     * 
     * @param player The player to show the animation to
     * @param coinflip The active coinflip data
     * @param gameId The ID of the game this animation is for
     */
    private void startRollingAnimation(Player player, ActiveCoinflip coinflip, UUID gameId) {
        String title = ConfigManager.colorize(rollingConfig.getString("gui.title", "&6&lCoinflip in Progress..."));
        int rows = rollingConfig.getInt("gui.rows", 3);
        Inventory inventory = Bukkit.createInventory(null, rows * 9, title);

        // Add player heads
        addPlayerHeads(inventory, coinflip);

        player.openInventory(inventory);

        // Start animation task
        int durationTicks = rollingConfig.getInt("animation.duration_ticks", 100);
        int switchInterval = rollingConfig.getInt("animation.switch_interval", 10);
        List<Integer> animatedSlots = rollingConfig.getIntegerList("animation.animated_slots");

        BukkitTask animationTask = new BukkitRunnable() {
            private int ticksElapsed = 0;
            private boolean showingCreatorColor = true;

            @Override
            public void run() {
                if (!player.isOnline() || player.getOpenInventory().getTopInventory() != inventory) {
                    cancel();
                    return;
                }

                // Switch colors at intervals
                if (ticksElapsed % switchInterval == 0) {
                    showingCreatorColor = !showingCreatorColor;
                    CoinflipColor currentColor = showingCreatorColor ? 
                            coinflip.getCreatorColor() : coinflip.getAccepterColor();

                    // Update animated slots
                    for (int slot : animatedSlots) {
                        ItemStack glassPane = new ItemStack(currentColor.getGlassPaneMaterial());
                        ItemMeta meta = glassPane.getItemMeta();
                        meta.setDisplayName(" ");
                        glassPane.setItemMeta(meta);
                        inventory.setItem(slot, glassPane);
                    }

                    // Play switch sound
                    playSound(player, "switch_sound", rollingConfig.getConfigurationSection("sounds"));
                }

                ticksElapsed++;

                // End animation
                if (ticksElapsed >= durationTicks) {
                    cancel();
                    // Play end sound
                    playSound(player, "end_sound", rollingConfig.getConfigurationSection("sounds"));

                    // Start winner animation
                    Bukkit.getScheduler().runTaskLater(plugin, () -> {
                        if (player.isOnline()) {
                            startWinnerAnimation(player, coinflip, gameId);
                        }
                    }, 10L); // Small delay before winner animation
                }
            }
        }.runTaskTimer(plugin, 0L, 1L);

        activeAnimations.put(player.getUniqueId(), animationTask);
    }

    /**
     * Start the winner animation for a player
     * 
     * @param player The player to show the animation to
     * @param coinflip The active coinflip data
     * @param gameId The ID of the game this animation is for
     */
    private void startWinnerAnimation(Player player, ActiveCoinflip coinflip, UUID gameId) {
        // Process the game results through the GameManager
        // This ensures results are processed even if players close the GUI
        plugin.getGameManager().processGameResults(gameId);

        // For backward compatibility, also mark this coinflip as processed
        UUID coinflipId = coinflip.getCreatorUUID();
        processedCoinflips.add(coinflipId);

        String winnerName = coinflip.getWinnerName();
        String title = ConfigManager.colorize(winnerConfig.getString("gui.title", "&a&l{winner_name} Wins!")
                .replace("{winner_name}", winnerName));

        // Use same inventory size as rolling GUI
        int rows = rollingConfig.getInt("gui.rows", 3);
        Inventory inventory = Bukkit.createInventory(null, rows * 9, title);

        // Add winner's head in center
        addWinnerHead(inventory, coinflip);

        player.openInventory(inventory);

        // Start winner animation
        int durationTicks = winnerConfig.getInt("animation.duration_ticks", 120);
        int fillInterval = winnerConfig.getInt("animation.fill_interval", 8);
        int maxLoops = winnerConfig.getInt("animation.max_loops", 3);
        int autoCloseDelay = winnerConfig.getInt("animation.auto_close_delay", 60);

        BukkitTask winnerTask = new BukkitRunnable() {
            private int ticksElapsed = 0;
            private int currentLoop = 0;
            private final Map<Integer, List<Integer>> fillOrderMap = getFillOrder(rows * 9);
            private int currentOrderIndex = 1; // Start with order 1

            @Override
            public void run() {
                if (!player.isOnline() || player.getOpenInventory().getTopInventory() != inventory) {
                    cancel();
                    return;
                }

                // Fill animation
                if (ticksElapsed % fillInterval == 0 && currentOrderIndex <= fillOrderMap.size()) {
                    // Get all slots for the current order
                    List<Integer> slotsToFill = fillOrderMap.getOrDefault(currentOrderIndex, Collections.emptyList());
                    int centerSlot = winnerConfig.getInt("winner_display.head_slot", 13);

                    // Fill all slots for this order
                    for (int slot : slotsToFill) {
                        // Skip center slot (winner's head)
                        if (slot != centerSlot) {
                            ItemStack glassPane = new ItemStack(coinflip.getWinnerColor().getGlassPaneMaterial());
                            ItemMeta meta = glassPane.getItemMeta();
                            meta.setDisplayName(" ");
                            glassPane.setItemMeta(meta);
                            inventory.setItem(slot, glassPane);
                        }
                    }

                    // Play fill sound once per order update
                    if (!slotsToFill.isEmpty()) {
                        playSound(player, "fill_sound", winnerConfig.getConfigurationSection("sounds"));
                    }

                    currentOrderIndex++;
                }

                ticksElapsed++;

                // Check if animation loop is complete
                if (currentOrderIndex > fillOrderMap.size()) {
                    currentLoop++;
                    if (maxLoops > 0 && currentLoop >= maxLoops) {
                        // Animation complete
                        playSound(player, "complete_sound", winnerConfig.getConfigurationSection("sounds"));

                        if (autoCloseDelay > 0) {
                            Bukkit.getScheduler().runTaskLater(plugin, () -> {
                                if (player.isOnline()) {
                                    player.closeInventory();
                                    playSound(player, "close_sound", winnerConfig.getConfigurationSection("sounds"));
                                }
                            }, autoCloseDelay);
                        }
                        cancel();
                        return;
                    }

                    // Reset for next loop
                    currentOrderIndex = 1; // Start with order 1 again
                    // Clear the inventory except winner head
                    for (int i = 0; i < inventory.getSize(); i++) {
                        if (i != winnerConfig.getInt("winner_display.head_slot", 13)) {
                            inventory.setItem(i, null);
                        }
                    }
                }
            }
        }.runTaskTimer(plugin, 0L, 1L);

        activeAnimations.put(player.getUniqueId(), winnerTask);
    }

    /**
     * Process coinflip results (money transfer, stats update)
     *
     * @deprecated This method is deprecated and will be removed in a future version.
     * Use {@link CoinflipGame#processResults()} instead.
     */
    @Deprecated
    private void processCoinflipResults(ActiveCoinflip coinflip) {
        // This method is kept for backward compatibility
        // Game results are now processed by the CoinflipGame class

        // If we have a game for this coinflip, process it through the game
        CoinflipGame game = plugin.getGameManager().getGameByPlayer(coinflip.getCreatorUUID());
        if (game != null) {
            plugin.getGameManager().processGameResults(game.getGameId());
            return;
        }

        // Legacy processing for old-style coinflips
        Player creator = Bukkit.getPlayer(coinflip.getCreatorUUID());
        Player accepter = Bukkit.getPlayer(coinflip.getAccepterUUID());

        if (creator == null || accepter == null) {
            return; // Handle offline players gracefully
        }

        // Update stats
        PlayerStats creatorStats = dataManager.getPlayerStats(coinflip.getCreatorUUID(), coinflip.getCreatorName());
        PlayerStats accepterStats = dataManager.getPlayerStats(coinflip.getAccepterUUID(), coinflip.getAccepterName());

        double betAmount = coinflip.getAmount();
        double tax = betAmount * configManager.getTaxPercentage() / 100;
        double winAmount = (betAmount * 2) - tax;

        // Get escrow manager
        EscrowManager escrowManager = plugin.getEscrowManager();

        if (coinflip.doesCreatorWin()) {
            // Creator wins
            creatorStats.addWin(betAmount);
            accepterStats.addLoss(betAmount);

            // Both bets are already in escrow, give winnings to creator
            escrowManager.removeFromEscrow(creator.getUniqueId(), winAmount);

            // Send messages
            creator.sendMessage(configManager.getMessage("won_coinflip")
                    .replace("{amount}", String.format("%.2f", winAmount))
                    .replace("{player}", accepter.getName()));

            accepter.sendMessage(configManager.getMessage("lost_coinflip")
                    .replace("{amount}", String.format("%.2f", betAmount))
                    .replace("{player}", creator.getName()));
        } else {
            // Accepter wins
            accepterStats.addWin(betAmount);
            creatorStats.addLoss(betAmount);

            // Both bets are already in escrow, give winnings to accepter
            escrowManager.removeFromEscrow(accepter.getUniqueId(), winAmount);

            // Send messages
            accepter.sendMessage(configManager.getMessage("won_coinflip")
                    .replace("{amount}", String.format("%.2f", winAmount))
                    .replace("{player}", creator.getName()));

            creator.sendMessage(configManager.getMessage("lost_coinflip")
                    .replace("{amount}", String.format("%.2f", betAmount))
                    .replace("{player}", accepter.getName()));
        }

        // Save updated stats
        dataManager.saveData();
    }

    /**
     * Add player heads to the rolling animation inventory
     */
    private void addPlayerHeads(Inventory inventory, ActiveCoinflip coinflip) {
        ConfigurationSection headsSection = rollingConfig.getConfigurationSection("player_heads");
        if (headsSection == null) return;

        // Creator head
        int creatorSlot = headsSection.getInt("creator_slot", 12);
        ItemStack creatorHead = new ItemStack(Material.PLAYER_HEAD);
        SkullMeta creatorMeta = (SkullMeta) creatorHead.getItemMeta();

        Player creator = Bukkit.getPlayer(coinflip.getCreatorUUID());
        if (creator != null) {
            creatorMeta.setOwningPlayer(creator);
        }

        String creatorName = headsSection.getString("creator_name", "&e{creator_name}")
                .replace("{creator_name}", coinflip.getCreatorName());
        creatorMeta.setDisplayName(ConfigManager.colorize(creatorName));

        List<String> creatorLore = new ArrayList<>();
        for (String line : headsSection.getStringList("creator_lore")) {
            creatorLore.add(ConfigManager.colorize(line
                    .replace("{creator_color}", coinflip.getCreatorColor().getColoredDisplayName())));
        }
        creatorMeta.setLore(creatorLore);
        creatorHead.setItemMeta(creatorMeta);
        inventory.setItem(creatorSlot, creatorHead);

        // Accepter head
        int accepterSlot = headsSection.getInt("accepter_slot", 14);
        ItemStack accepterHead = new ItemStack(Material.PLAYER_HEAD);
        SkullMeta accepterMeta = (SkullMeta) accepterHead.getItemMeta();

        Player accepter = Bukkit.getPlayer(coinflip.getAccepterUUID());
        if (accepter != null) {
            accepterMeta.setOwningPlayer(accepter);
        }

        String accepterName = headsSection.getString("accepter_name", "&e{accepter_name}")
                .replace("{accepter_name}", coinflip.getAccepterName());
        accepterMeta.setDisplayName(ConfigManager.colorize(accepterName));

        List<String> accepterLore = new ArrayList<>();
        for (String line : headsSection.getStringList("accepter_lore")) {
            accepterLore.add(ConfigManager.colorize(line
                    .replace("{accepter_color}", coinflip.getAccepterColor().getColoredDisplayName())));
        }
        accepterMeta.setLore(accepterLore);
        accepterHead.setItemMeta(accepterMeta);
        inventory.setItem(accepterSlot, accepterHead);

        // VS indicator
        int vsSlot = headsSection.getInt("vs_slot", 13);
        String vsMaterialName = headsSection.getString("vs_material", "DIAMOND");
        Material vsMaterial;
        try {
            vsMaterial = Material.valueOf(vsMaterialName.toUpperCase());
        } catch (IllegalArgumentException e) {
            vsMaterial = Material.DIAMOND;
        }

        ItemStack vsItem = new ItemStack(vsMaterial);
        ItemMeta vsMeta = vsItem.getItemMeta();
        vsMeta.setDisplayName(ConfigManager.colorize(headsSection.getString("vs_name", "&f&lVS")));
        vsMeta.setLore(ConfigManager.colorizeList(headsSection.getStringList("vs_lore")));
        vsItem.setItemMeta(vsMeta);
        inventory.setItem(vsSlot, vsItem);
    }

    /**
     * Add winner's head to the winner animation inventory
     */
    private void addWinnerHead(Inventory inventory, ActiveCoinflip coinflip) {
        ConfigurationSection winnerSection = winnerConfig.getConfigurationSection("winner_display");
        if (winnerSection == null) return;

        int headSlot = winnerSection.getInt("head_slot", 13);
        ItemStack winnerHead = new ItemStack(Material.PLAYER_HEAD);
        SkullMeta winnerMeta = (SkullMeta) winnerHead.getItemMeta();

        Player winner = Bukkit.getPlayer(coinflip.getWinnerUUID());
        if (winner != null) {
            winnerMeta.setOwningPlayer(winner);
        }

        String headName = winnerSection.getString("head_name", "&a&l{winner_name} Wins!")
                .replace("{winner_name}", coinflip.getWinnerName());
        winnerMeta.setDisplayName(ConfigManager.colorize(headName));

        List<String> headLore = new ArrayList<>();
        double tax = coinflip.getAmount() * configManager.getTaxPercentage() / 100;
        double winAmount = (coinflip.getAmount() * 2) - tax;

        for (String line : winnerSection.getStringList("head_lore")) {
            headLore.add(ConfigManager.colorize(line
                    .replace("{winner_name}", coinflip.getWinnerName())
                    .replace("{amount}", String.format("%.2f", winAmount))
                    .replace("{winner_color}", coinflip.getWinnerColor().getColoredDisplayName())));
        }
        winnerMeta.setLore(headLore);
        winnerHead.setItemMeta(winnerMeta);
        inventory.setItem(headSlot, winnerHead);
    }

    /**
     * Get the fill order for winner animation
     * @return A map where the key is the update order and the value is a list of slots to update in that order
     */
    private Map<Integer, List<Integer>> getFillOrder(int inventorySize) {
        Map<Integer, List<Integer>> fillOrderMap = new HashMap<>();

        if (inventorySize == 27) { // 3 rows
            ConfigurationSection patternSection = winnerConfig.getConfigurationSection("fill_pattern.pattern_27");
            if (patternSection != null) {
                for (String key : patternSection.getKeys(false)) {
                    try {
                        int slot = Integer.parseInt(key);
                        int order = patternSection.getInt(key);

                        // Add the slot to the list for this order
                        fillOrderMap.computeIfAbsent(order, k -> new ArrayList<>()).add(slot);
                    } catch (NumberFormatException ignored) {}
                }
            }
        }

        // Fallback: simple order
        if (fillOrderMap.isEmpty()) {
            for (int i = 0; i < inventorySize; i++) {
                // Each slot gets its own update order
                fillOrderMap.computeIfAbsent(i, k -> new ArrayList<>()).add(i);
            }
        }

        return fillOrderMap;
    }

    /**
     * Play a sound to a player
     */
    private void playSound(Player player, String soundKey, ConfigurationSection soundsSection) {
        if (soundsSection == null) return;

        String soundName = soundsSection.getString(soundKey);
        if (soundName == null) return;

        try {
            Sound sound = Sound.valueOf(soundName);
            float volume = (float) soundsSection.getDouble(soundKey.replace("_sound", "_volume"), 1.0);
            float pitch = (float) soundsSection.getDouble(soundKey.replace("_sound", "_pitch"), 1.0);
            player.playSound(player.getLocation(), sound, volume, pitch);
        } catch (IllegalArgumentException e) {
            plugin.getLogger().warning("Invalid sound: " + soundName);
        }
    }

    /**
     * Cancel animation for a player
     */
    public void cancelAnimation(UUID playerUUID) {
        BukkitTask task = activeAnimations.remove(playerUUID);
        if (task != null) {
            task.cancel();
        }

        // Check if player was in an active coinflip
        ActiveCoinflip coinflip = activeCoinflips.remove(playerUUID);

        // Check if player is in a game
        CoinflipGame game = plugin.getGameManager().getGameByPlayer(playerUUID);
        if (game != null) {
            // Check if we should cancel the bet when animation is cancelled
            boolean cancelBetOnAnimationCancel = configManager.getConfig().getBoolean("settings.animations.cancel_bet_on_animation_cancel", true);

            // If the game is still in progress
            if (game.getStatus() == CoinflipGame.CoinflipStatus.PENDING || 
                game.getStatus() == CoinflipGame.CoinflipStatus.ROLLING) {

                if (cancelBetOnAnimationCancel) {
                    // Cancel the game and refund both players
                    plugin.getGameManager().cancelGame(game.getGameId());

                    // Close the other player's GUI if they're still in the animation
                    UUID otherPlayerUUID = game.getCreatorUUID().equals(playerUUID) 
                        ? game.getAccepterUUID() : game.getCreatorUUID();

                    Player otherPlayer = Bukkit.getPlayer(otherPlayerUUID);
                    if (otherPlayer != null && otherPlayer.isOnline()) {
                        // Send message to the other player
                        otherPlayer.sendMessage(configManager.getMessage("coinflip_other_player_left"));

                        // Close their inventory if they have one open
                        if (isPlayerInAnimation(otherPlayerUUID)) {
                            otherPlayer.closeInventory();
                        }
                    }
                }
                // If cancelBetOnAnimationCancel is false, we don't cancel the game
                // The game will continue and results will be processed normally
            }
        } 
        // For backward compatibility, handle old-style coinflips
        else if (coinflip != null) {
            // Get the coinflip ID (creator UUID)
            UUID coinflipId = coinflip.getCreatorUUID();

            // If the coinflip hasn't been processed yet, return money to players
            if (!processedCoinflips.contains(coinflipId)) {
                boolean cancelBetOnAnimationCancel = configManager.getConfig().getBoolean("settings.animations.cancel_bet_on_animation_cancel", true);

                if (cancelBetOnAnimationCancel) {
                    EscrowManager escrowManager = plugin.getEscrowManager();

                    // Return creator's bet
                    Player creator = Bukkit.getPlayer(coinflip.getCreatorUUID());
                    if (creator != null && creator.isOnline()) {
                        escrowManager.removeFromEscrow(creator.getUniqueId(), coinflip.getAmount());
                        creator.sendMessage(configManager.getMessage("coinflip_bet_cancelled"));
                    }

                    // Return accepter's bet if they've already placed it
                    Player accepter = Bukkit.getPlayer(coinflip.getAccepterUUID());
                    if (accepter != null && accepter.isOnline()) {
                        escrowManager.removeFromEscrow(accepter.getUniqueId(), coinflip.getAmount());
                        accepter.sendMessage(configManager.getMessage("coinflip_bet_cancelled"));

                        // Close their inventory if they have one open and they're not the one who triggered this
                        if (!accepter.getUniqueId().equals(playerUUID) && isPlayerInAnimation(accepter.getUniqueId())) {
                            accepter.closeInventory();
                        }
                    }

                    // Mark as processed to prevent double refunds
                    processedCoinflips.add(coinflipId);
                }
                // If cancelBetOnAnimationCancel is false, we don't cancel the coinflip
                // The coinflip will continue and results will be processed normally
            }
        }
    }

    /**
     * Check if a player is in an animation
     */
    public boolean isPlayerInAnimation(UUID playerUUID) {
        return activeAnimations.containsKey(playerUUID);
    }

    /**
     * Clean up all animations (for plugin disable)
     */
    public void cleanup() {
        for (BukkitTask task : activeAnimations.values()) {
            task.cancel();
        }
        activeAnimations.clear();
        activeCoinflips.clear();
        processedCoinflips.clear();
    }

    /**
     * Get the rolling configuration
     */
    public FileConfiguration getRollingConfig() {
        return rollingConfig;
    }

    /**
     * Get the winner configuration
     */
    public FileConfiguration getWinnerConfig() {
        return winnerConfig;
    }
}
