package ca.xef5000.ariesCoinflip.managers;

import ca.xef5000.ariesCoinflip.AriesCoinflip;
import ca.xef5000.ariesCoinflip.models.currencies.Currency;
import ca.xef5000.ariesCoinflip.models.currencies.CurrencyIntegrations;
import ca.xef5000.ariesCoinflip.utils.ConfigManager;

import java.util.ArrayList;
import java.util.List;

public class CurrencyManager {

    private final AriesCoinflip plugin;
    private final ConfigManager configManager;
    private final List<Currency> currencies = new ArrayList<>();

    public CurrencyManager(AriesCoinflip plugin, ConfigManager configManager) {
        this.plugin = plugin;
        this.configManager = configManager;
        reloadConfig();
    }

    public void reloadConfig() {
        currencies.clear();
        plugin.reloadConfig();
        for (String currencyName : configManager.getConfig().getConfigurationSection("currencies").getKeys(false)) {
            Currency currency = new Currency(
                    currencyName,
                    configManager.getConfig().getString("currencies." + currencyName + ".symbol"),
                    configManager.getConfig().getDouble("currencies." + currencyName + ".min_bet"),
                    configManager.getConfig().getDouble("currencies." + currencyName + ".max_bet"),
                    CurrencyIntegrations.valueOf(configManager.getConfig().getString("currencies." + currencyName + ".integration").toUpperCase()).createIntegration(plugin)
            );
            currencies.add(currency);
        }
    }

    public Currency getCurrency(String currencyName) {
        for (Currency currency : currencies) {
            if (currency.getName().equalsIgnoreCase(currencyName)) {
                return currency;
            }
        }
        return null;
    }

    public List<Currency> getCurrencies() {
        return currencies;
    }

    /**
     * Check if a currency is valid
     *
     * @param currencyName The currency name to check
     * @return true if the currency exists and is valid
     */
    public boolean isValidCurrency(String currencyName) {
        return getCurrency(currencyName) != null;
    }

    /**
     * Get the default currency (first one in the list)
     *
     * @return The default currency, or null if no currencies are configured
     */
    public Currency getDefaultCurrency() {
        return currencies.isEmpty() ? null : currencies.get(0);
    }
}
