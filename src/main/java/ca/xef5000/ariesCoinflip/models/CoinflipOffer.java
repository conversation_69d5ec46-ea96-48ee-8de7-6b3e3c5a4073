package ca.xef5000.ariesCoinflip.models;

import org.bukkit.entity.Player;

import java.util.UUID;

public class CoinflipOffer {
    private final UUID playerUUID;
    private final String playerName;
    private final double amount;
    private final long creationTime;
    private final int timeoutSeconds;
    private final CoinflipColor selectedColor;
    private final String currency;

    public CoinflipOffer(Player player, double amount, int timeoutSeconds, CoinflipColor selectedColor, String currency) {
        this.playerUUID = player.getUniqueId();
        this.playerName = player.getName();
        this.amount = amount;
        this.creationTime = System.currentTimeMillis();
        this.timeoutSeconds = timeoutSeconds;
        this.selectedColor = selectedColor;
        this.currency = currency;
    }

    /**
     * Constructor for loading from database
     */
    public CoinflipOffer(UUID playerUUID, String playerName, double amount, long creationTime, int timeoutSeconds, CoinflipColor selectedColor, String currency) {
        this.playerUUID = playerUUID;
        this.playerName = playerName;
        this.amount = amount;
        this.creationTime = creationTime;
        this.timeoutSeconds = timeoutSeconds;
        this.selectedColor = selectedColor;
        this.currency = currency;
    }

    /**
     * Legacy constructor for backward compatibility (uses default currency)
     */
    @Deprecated
    public CoinflipOffer(Player player, double amount, int timeoutSeconds, CoinflipColor selectedColor) {
        this(player, amount, timeoutSeconds, selectedColor, "money"); // Default currency
    }

    /**
     * Legacy constructor for loading from database (backward compatibility)
     */
    @Deprecated
    public CoinflipOffer(UUID playerUUID, String playerName, double amount, long creationTime, int timeoutSeconds, CoinflipColor selectedColor) {
        this(playerUUID, playerName, amount, creationTime, timeoutSeconds, selectedColor, "money"); // Default currency
    }

    /**
     * Legacy constructor for loading from database (backward compatibility)
     */
    @Deprecated
    public CoinflipOffer(UUID playerUUID, String playerName, double amount, long creationTime, int timeoutSeconds) {
        this(playerUUID, playerName, amount, creationTime, timeoutSeconds, CoinflipColor.RED, "money"); // Default color and currency
    }

    public UUID getPlayerUUID() {
        return playerUUID;
    }

    public String getPlayerName() {
        return playerName;
    }

    public double getAmount() {
        return amount;
    }

    public long getCreationTime() {
        return creationTime;
    }

    public boolean isExpired() {
        long currentTime = System.currentTimeMillis();
        long expirationTime = creationTime + (timeoutSeconds * 1000L);
        return currentTime > expirationTime;
    }

    public long getTimeRemaining() {
        long currentTime = System.currentTimeMillis();
        long expirationTime = creationTime + (timeoutSeconds * 1000L);
        return Math.max(0, expirationTime - currentTime);
    }

    public int getTimeRemainingSeconds() {
        return (int) (getTimeRemaining() / 1000);
    }

    /**
     * Get the timeout seconds for this offer
     *
     * @return The timeout seconds
     */
    public int getTimeoutSeconds() {
        return timeoutSeconds;
    }

    /**
     * Get the selected color for this offer
     *
     * @return The selected color
     */
    public CoinflipColor getSelectedColor() {
        return selectedColor;
    }
}
