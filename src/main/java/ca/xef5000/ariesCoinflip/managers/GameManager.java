package ca.xef5000.ariesCoinflip.managers;

import ca.xef5000.ariesCoinflip.AriesCoinflip;
import ca.xef5000.ariesCoinflip.models.CoinflipColor;
import ca.xef5000.ariesCoinflip.models.CoinflipGame;
import ca.xef5000.ariesCoinflip.models.CoinflipOffer;
import org.bukkit.entity.Player;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Manages coinflip games, handling their lifecycle independently of the UI.
 * This class serves as the backend for the coinflip system.
 */
public class GameManager {
    private final AriesCoinflip plugin;
    private final Map<UUID, CoinflipGame> activeGames = new HashMap<>(); // Player UUID -> Game
    private final Map<UUID, UUID> playerGameMap = new HashMap<>(); // Player UUID -> Game ID

    public GameManager(AriesCoinflip plugin) {
        this.plugin = plugin;
    }

    /**
     * Create and start a new coinflip game
     * 
     * @param creator The player who created the coinflip
     * @param accepter The player who accepted the coinflip
     * @param amount The bet amount
     * @param creatorColor The color selected by the creator
     * @param accepterColor The color selected by the accepter
     * @return The created game
     */
    public CoinflipGame createGame(Player creator, Player accepter, double amount, 
                                  CoinflipColor creatorColor, CoinflipColor accepterColor, boolean forceWin) {
        // Create the game
        CoinflipGame game = new CoinflipGame(creator, accepter, amount, creatorColor, accepterColor, plugin, forceWin);
        
        // Store the game
        activeGames.put(game.getGameId(), game);
        
        // Map both players to this game
        playerGameMap.put(creator.getUniqueId(), game.getGameId());
        playerGameMap.put(accepter.getUniqueId(), game.getGameId());
        
        return game;
    }

    /**
     * Start a game from an existing offer
     * 
     * @param creator The player who created the offer
     * @param accepter The player who accepted the offer
     * @param offer The coinflip offer
     * @param accepterColor The color selected by the accepter
     * @return The created game
     */
    public CoinflipGame startGameFromOffer(Player creator, Player accepter, CoinflipOffer offer, CoinflipColor accepterColor, boolean forceWin) {
        // Remove the offer
        plugin.getDataManager().removeCoinflipOffer(offer.getPlayerUUID(), false);
        
        // Create the game
        return createGame(creator, accepter, offer.getAmount(), offer.getSelectedColor(), accepterColor, forceWin);
    }

    /**
     * Process the results of a game
     * 
     * @param gameId The ID of the game to process
     * @return true if results were processed, false if already processed
     */
    public boolean processGameResults(UUID gameId) {
        CoinflipGame game = activeGames.get(gameId);
        if (game != null) {
            return game.processResults();
        }
        return false;
    }

    /**
     * Cancel a game and refund players
     * 
     * @param gameId The ID of the game to cancel
     */
    public void cancelGame(UUID gameId) {
        CoinflipGame game = activeGames.get(gameId);
        if (game != null) {
            game.cancelGame();
            
            // Clean up maps
            playerGameMap.remove(game.getCreatorUUID());
            playerGameMap.remove(game.getAccepterUUID());
            activeGames.remove(gameId);
        }
    }

    /**
     * Get a game by its ID
     * 
     * @param gameId The ID of the game to get
     * @return The game, or null if not found
     */
    public CoinflipGame getGame(UUID gameId) {
        return activeGames.get(gameId);
    }

    /**
     * Get a game by a player involved in it
     * 
     * @param playerUUID The UUID of the player
     * @return The game the player is involved in, or null if not found
     */
    public CoinflipGame getGameByPlayer(UUID playerUUID) {
        UUID gameId = playerGameMap.get(playerUUID);
        if (gameId != null) {
            return activeGames.get(gameId);
        }
        return null;
    }

    /**
     * Check if a player is in an active game
     * 
     * @param playerUUID The UUID of the player
     * @return true if the player is in an active game, false otherwise
     */
    public boolean isPlayerInGame(UUID playerUUID) {
        return playerGameMap.containsKey(playerUUID);
    }

    /**
     * Remove a completed game
     * 
     * @param gameId The ID of the game to remove
     */
    public void removeGame(UUID gameId) {
        CoinflipGame game = activeGames.get(gameId);
        if (game != null) {
            // Only remove if the game is completed or cancelled
            if (game.getStatus() == CoinflipGame.CoinflipStatus.COMPLETED || 
                game.getStatus() == CoinflipGame.CoinflipStatus.CANCELLED) {
                
                // Clean up maps
                playerGameMap.remove(game.getCreatorUUID());
                playerGameMap.remove(game.getAccepterUUID());
                activeGames.remove(gameId);
            }
        }
    }

    /**
     * Handle a player disconnecting
     * 
     * @param playerUUID The UUID of the player who disconnected
     */
    public void handlePlayerDisconnect(UUID playerUUID) {
        CoinflipGame game = getGameByPlayer(playerUUID);
        if (game != null) {
            // If the game is still in progress, cancel it
            if (game.getStatus() == CoinflipGame.CoinflipStatus.PENDING || 
                game.getStatus() == CoinflipGame.CoinflipStatus.ROLLING) {
                cancelGame(game.getGameId());
            }
        }
    }

    /**
     * Clean up all games (for plugin disable)
     */
    public void cleanup() {
        // Process or cancel all active games
        for (CoinflipGame game : activeGames.values()) {
            if (game.getStatus() == CoinflipGame.CoinflipStatus.COMPLETED) {
                // Make sure completed games have their results processed
                game.processResults();
            } else {
                // Cancel all other games
                game.cancelGame();
            }
        }
        
        // Clear maps
        activeGames.clear();
        playerGameMap.clear();
    }
}