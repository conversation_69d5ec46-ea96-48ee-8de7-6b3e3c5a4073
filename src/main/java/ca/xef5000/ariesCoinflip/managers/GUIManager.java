package ca.xef5000.ariesCoinflip.managers;

import ca.xef5000.ariesCoinflip.AriesCoinflip;
import ca.xef5000.ariesCoinflip.models.CoinflipOffer;
import ca.xef5000.ariesCoinflip.models.PlayerStats;
import ca.xef5000.ariesCoinflip.utils.ConfigManager;
import com.nexomc.nexo.api.NexoItems;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.inventory.meta.SkullMeta;

import java.io.File;
import java.text.DecimalFormat;
import java.util.*;

public class GUIManager {
    private final AriesCoinflip plugin;
    private final ConfigManager configManager;
    private final DataManager dataManager;
    private final Map<UUID, Inventory> openInventories;
    private final DecimalFormat decimalFormat;
    private FileConfiguration statsConfig;

    public GUIManager(AriesCoinflip plugin, ConfigManager configManager, DataManager dataManager) {
        this.plugin = plugin;
        this.configManager = configManager;
        this.dataManager = dataManager;
        this.openInventories = new HashMap<>();
        this.decimalFormat = new DecimalFormat("#,##0.00");
        reloadConfig();
    }

    public void reloadConfig() {
        File configFile = new File(plugin.getDataFolder(), "stats_gui.yml");
        if (!configFile.exists()) {
            plugin.saveResource("stats_gui.yml", false);
        }
        statsConfig = YamlConfiguration.loadConfiguration(configFile);
    }

    public void openMainMenu(Player player) {
        int rows = configManager.getGuiRows();
        String title = configManager.getGuiTitle();
        Inventory inventory = Bukkit.createInventory(null, rows * 9, title);

        // Add static items
        addStaticItems(inventory);

        // Add decorative items
        addDecorativeItems(inventory);

        // Add player statistics item
        addStatisticsItem(inventory, player);

        // Add active coinflip offers
        addCoinflipOffers(inventory);

        player.openInventory(inventory);
        openInventories.put(player.getUniqueId(), inventory);
    }

    public void openStatisticsMenu(Player player) {
        String title = ConfigManager.colorize(statsConfig.getString("gui.title"));
        Inventory inventory = Bukkit.createInventory(null, statsConfig.getInt("gui.rows") * 9, title);

        PlayerStats stats = dataManager.getPlayerStats(player);

        // Load all items
        ConfigurationSection itemsSection = statsConfig.getConfigurationSection("gui.items");
        if (itemsSection != null) {
            for (String key : itemsSection.getKeys(false)) {
                if (!itemsSection.getBoolean(key + ".enabled", true)) continue;

                int slot = itemsSection.getInt(key + ".slot", 0);
                String materialName = itemsSection.getString(key + ".material", "STONE");
                String name = itemsSection.getString(key + ".name", "");
                List<String> lore = itemsSection.getStringList(key + ".lore");
                List<String> formattedLore = new ArrayList<>();
                for (String line : lore) {
                    line = line.replace("{games_played}", String.valueOf(stats.getGamesPlayed()))
                            .replace("{games_won}", String.valueOf(stats.getGamesWon()))
                            .replace("{games_lost}", String.valueOf(stats.getGamesLost()))
                            .replace("{win_rate}", String.format("%.2f", stats.getWinRate()))
                            .replace("{total_won}", decimalFormat.format(stats.getTotalWon()))
                            .replace("{total_lost}", decimalFormat.format(stats.getTotalWon()))
                            .replace("{net_profit}", decimalFormat.format(Math.abs(stats.getNetProfit())))
                            .replace("{luck}", String.valueOf(stats.getLuck()))
                    ;
                    formattedLore.add(line);
                }
                lore = formattedLore;

                ItemStack item;
                if (materialName.toLowerCase().startsWith("nexo:")) {
                    // This is a Nexo item
                    String itemId = materialName.substring(5); // Remove "nexo:" prefix
                    try {
                        item = NexoItems.itemFromId(itemId).build();
                        if (item == null) {
                            plugin.getLogger().warning("Invalid Nexo item ID: " + itemId + " for item: " + key);
                            item = new ItemStack(Material.STONE);
                        }
                    } catch (Exception e) {
                        plugin.getLogger().warning("Error getting Nexo item: " + itemId + " for item: " + key + ". Error: " + e.getMessage());
                        item = new ItemStack(Material.STONE);
                    }
                } else {
                    // This is a regular Minecraft material
                    Material material;
                    try {
                        material = Material.valueOf(materialName.toUpperCase());
                        item = new ItemStack(material);
                        if (material.equals(Material.PLAYER_HEAD)) {
                            SkullMeta meta = ((SkullMeta) item.getItemMeta());
                            meta.setOwningPlayer(player);
                            item.setItemMeta(meta);
                        }
                    } catch (IllegalArgumentException e) {
                        plugin.getLogger().warning("Invalid material: " + materialName + " for item: " + key);
                        item = new ItemStack(Material.STONE);
                    }
                }

                ItemMeta meta = item.getItemMeta();
                if (meta != null) {
                    meta.setDisplayName(ConfigManager.colorize(name));
                    meta.setLore(ConfigManager.colorizeList(lore));
                    item.setItemMeta(meta);
                }

                inventory.setItem(slot, item);
            }
        }

        player.openInventory(inventory);
    }

    private void addStaticItems(Inventory inventory) {
        ConfigurationSection itemsSection = configManager.getConfig().getConfigurationSection("gui.items");
        if (itemsSection == null) return;

        for (String key : itemsSection.getKeys(false)) {
            if (!itemsSection.getBoolean(key + ".enabled", true)) continue;

            int slot = itemsSection.getInt(key + ".slot", 0);
            String materialName = itemsSection.getString(key + ".material", "STONE");
            String name = itemsSection.getString(key + ".name", "");
            List<String> lore = itemsSection.getStringList(key + ".lore");

            ItemStack item;
            if (materialName.toLowerCase().startsWith("nexo:")) {
                // This is a Nexo item
                String itemId = materialName.substring(5); // Remove "nexo:" prefix
                try {
                    item = NexoItems.itemFromId(itemId).build();
                    if (item == null) {
                        plugin.getLogger().warning("Invalid Nexo item ID: " + itemId + " for item: " + key);
                        item = new ItemStack(Material.STONE);
                    }
                } catch (Exception e) {
                    plugin.getLogger().warning("Error getting Nexo item: " + itemId + " for item: " + key + ". Error: " + e.getMessage());
                    item = new ItemStack(Material.STONE);
                }
            } else {
                // This is a regular Minecraft material
                Material material;
                try {
                    material = Material.valueOf(materialName.toUpperCase());
                    item = new ItemStack(material);
                } catch (IllegalArgumentException e) {
                    plugin.getLogger().warning("Invalid material: " + materialName + " for item: " + key);
                    item = new ItemStack(Material.STONE);
                }
            }
            ItemMeta meta = item.getItemMeta();
            meta.setDisplayName(ConfigManager.colorize(name));
            meta.setLore(ConfigManager.colorizeList(lore));
            item.setItemMeta(meta);

            inventory.setItem(slot, item);
        }
    }

    private void addDecorativeItems(Inventory inventory) {
        ConfigurationSection decorativeSection = configManager.getConfig().getConfigurationSection("gui.decorative");
        if (decorativeSection == null) return;

        for (String key : decorativeSection.getKeys(false)) {
            if (!decorativeSection.getBoolean(key + ".enabled", true)) continue;

            if (decorativeSection.contains(key + ".slots")) {
                // Multiple slots
                List<Integer> slots = decorativeSection.getIntegerList(key + ".slots");
                String materialName = decorativeSection.getString(key + ".material", "STONE");
                String name = decorativeSection.getString(key + ".name", "");
                List<String> lore = decorativeSection.getStringList(key + ".lore");

                ItemStack item;
                if (materialName.toLowerCase().startsWith("nexo:")) {
                    // This is a Nexo item
                    String itemId = materialName.substring(5); // Remove "nexo:" prefix
                    try {
                        item = NexoItems.itemFromId(itemId).build();
                        if (item == null) {
                            plugin.getLogger().warning("Invalid Nexo item ID: " + itemId + " for decorative item: " + key);
                            item = new ItemStack(Material.STONE);
                        }
                    } catch (Exception e) {
                        plugin.getLogger().warning("Error getting Nexo item: " + itemId + " for decorative item: " + key + ". Error: " + e.getMessage());
                        item = new ItemStack(Material.STONE);
                    }
                } else {
                    // This is a regular Minecraft material
                    Material material;
                    try {
                        material = Material.valueOf(materialName.toUpperCase());
                        item = new ItemStack(material);
                    } catch (IllegalArgumentException e) {
                        plugin.getLogger().warning("Invalid material: " + materialName + " for decorative item: " + key);
                        item = new ItemStack(Material.STONE);
                    }
                }
                ItemMeta meta = item.getItemMeta();
                meta.setDisplayName(ConfigManager.colorize(name));
                meta.setLore(ConfigManager.colorizeList(lore));
                item.setItemMeta(meta);

                for (int slot : slots) {
                    inventory.setItem(slot, item);
                }
            } else {
                // Single slot
                int slot = decorativeSection.getInt(key + ".slot", 0);
                String materialName = decorativeSection.getString(key + ".material", "STONE");
                String name = decorativeSection.getString(key + ".name", "");
                List<String> lore = decorativeSection.getStringList(key + ".lore");

                ItemStack item;
                if (materialName.toLowerCase().startsWith("nexo:")) {
                    // This is a Nexo item
                    String itemId = materialName.substring(5); // Remove "nexo:" prefix
                    try {
                        item = NexoItems.itemFromId(itemId).build();
                        if (item == null) {
                            plugin.getLogger().warning("Invalid Nexo item ID: " + itemId + " for decorative item: " + key);
                            item = new ItemStack(Material.STONE);
                        }
                    } catch (Exception e) {
                        plugin.getLogger().warning("Error getting Nexo item: " + itemId + " for decorative item: " + key + ". Error: " + e.getMessage());
                        item = new ItemStack(Material.STONE);
                    }
                } else {
                    // This is a regular Minecraft material
                    Material material;
                    try {
                        material = Material.valueOf(materialName.toUpperCase());
                        item = new ItemStack(material);
                    } catch (IllegalArgumentException e) {
                        plugin.getLogger().warning("Invalid material: " + materialName + " for decorative item: " + key);
                        item = new ItemStack(Material.STONE);
                    }
                }
                ItemMeta meta = item.getItemMeta();
                meta.setDisplayName(ConfigManager.colorize(name));
                meta.setLore(ConfigManager.colorizeList(lore));
                item.setItemMeta(meta);

                inventory.setItem(slot, item);
            }
        }
    }

    private void addStatisticsItem(Inventory inventory, Player player) {
        ConfigurationSection statsSection = configManager.getConfig().getConfigurationSection("gui.items.statistics");
        if (statsSection == null || !statsSection.getBoolean("enabled", true)) return;

        int slot = statsSection.getInt("slot", 8);
        String name = statsSection.getString("name", "&a&lYour Statistics");
        List<String> lore = new ArrayList<>(statsSection.getStringList("lore"));

        PlayerStats stats = dataManager.getPlayerStats(player);

        // Replace placeholders in lore
        List<String> formattedLore = new ArrayList<>();
        for (String line : lore) {
            line = line.replace("{games_played}", String.valueOf(stats.getGamesPlayed()))
                    .replace("{games_won}", String.valueOf(stats.getGamesWon()))
                    .replace("{games_lost}", String.valueOf(stats.getGamesLost()))
                    .replace("{win_rate}", String.format("%.2f", stats.getWinRate()));
            formattedLore.add(line);
        }

        ItemStack playerHead = new ItemStack(Material.PLAYER_HEAD);
        SkullMeta skullMeta = (SkullMeta) playerHead.getItemMeta();
        skullMeta.setOwningPlayer(player);
        skullMeta.setDisplayName(ConfigManager.colorize(name));
        skullMeta.setLore(ConfigManager.colorizeList(formattedLore));
        playerHead.setItemMeta(skullMeta);

        inventory.setItem(slot, playerHead);
    }

    private void addCoinflipOffers(Inventory inventory) {
        List<CoinflipOffer> activeOffers = dataManager.getActiveOffers();

        // Get offer-item configuration
        ConfigurationSection offerItemSection = configManager.getConfig().getConfigurationSection("gui.items.offer-item");
        if (offerItemSection == null || !offerItemSection.getBoolean("enabled", true)) return;

        String materialName = offerItemSection.getString("material", "PLAYER_HEAD");
        String nameFormat = offerItemSection.getString("name", "&e{player}'s Coinflip");
        List<String> loreFormat = offerItemSection.getStringList("lore");
        List<Integer> slots = offerItemSection.getIntegerList("slots");

        // Clear previous offers from all configured slots
        for (int slot : slots) {
            inventory.setItem(slot, null);
        }

        // Add current offers
        int slotIndex = 0;
        for (CoinflipOffer offer : activeOffers) {
            if (slotIndex >= slots.size()) break; // Don't exceed the available slots

            Player offerPlayer = Bukkit.getPlayer(offer.getPlayerUUID());
            if (offerPlayer == null) continue; // Skip if player is offline

            // Create the item
            ItemStack item;
            if (materialName.equalsIgnoreCase("PLAYER_HEAD")) {
                item = new ItemStack(Material.PLAYER_HEAD);
                SkullMeta skullMeta = (SkullMeta) item.getItemMeta();
                skullMeta.setOwningPlayer(offerPlayer);
                item.setItemMeta(skullMeta);
            } else if (materialName.toLowerCase().startsWith("nexo:")) {
                // This is a Nexo item
                String itemId = materialName.substring(5); // Remove "nexo:" prefix
                try {
                    item = NexoItems.itemFromId(itemId).build();
                    if (item == null) {
                        plugin.getLogger().warning("Invalid Nexo item ID: " + itemId + " for offer-item");
                        item = new ItemStack(Material.PLAYER_HEAD);
                        SkullMeta skullMeta = (SkullMeta) item.getItemMeta();
                        skullMeta.setOwningPlayer(offerPlayer);
                        item.setItemMeta(skullMeta);
                    }
                } catch (Exception e) {
                    plugin.getLogger().warning("Error getting Nexo item: " + itemId + " for offer-item. Error: " + e.getMessage());
                    item = new ItemStack(Material.PLAYER_HEAD);
                    SkullMeta skullMeta = (SkullMeta) item.getItemMeta();
                    skullMeta.setOwningPlayer(offerPlayer);
                    item.setItemMeta(skullMeta);
                }
            } else {
                // This is a regular Minecraft material
                Material material;
                try {
                    material = Material.valueOf(materialName.toUpperCase());
                    item = new ItemStack(material);
                } catch (IllegalArgumentException e) {
                    plugin.getLogger().warning("Invalid material: " + materialName + " for offer-item");
                    item = new ItemStack(Material.PLAYER_HEAD);
                    SkullMeta skullMeta = (SkullMeta) item.getItemMeta();
                    skullMeta.setOwningPlayer(offerPlayer);
                    item.setItemMeta(skullMeta);
                }
            }

            // Set the item's display name and lore
            ItemMeta meta = item.getItemMeta();

            // Replace placeholders in name
            String name = nameFormat.replace("{player}", offer.getPlayerName());
            meta.setDisplayName(ConfigManager.colorize(name));

            // Replace placeholders in lore
            List<String> lore = new ArrayList<>();
            for (String line : loreFormat) {
                line = line.replace("{player}", offer.getPlayerName())
                        .replace("{amount}", decimalFormat.format(offer.getAmount()))
                        .replace("{time_remaining}", formatTime(offer.getTimeRemainingSeconds()))
                        .replace("{color}", offer.getSelectedColor().getColoredDisplayName());
                lore.add(line);
            }

            meta.setLore(ConfigManager.colorizeList(lore));
            item.setItemMeta(meta);

            // Add the item to the inventory
            inventory.setItem(slots.get(slotIndex), item);
            slotIndex++;
        }
    }

    public void updateOpenInventories() {
        Iterator<Map.Entry<UUID, Inventory>> iterator = openInventories.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<UUID, Inventory> entry = iterator.next();
            UUID playerUUID = entry.getKey();
            Inventory inventory = entry.getValue();

            Player player = Bukkit.getPlayer(playerUUID);
            if (player == null || !player.isOnline()) {
                iterator.remove();
                continue;
            }

            // Update coinflip offers
            addCoinflipOffers(inventory);
        }
    }

    private String formatTime(int seconds) {
        int minutes = seconds / 60;
        int remainingSeconds = seconds % 60;
        return String.format("%d:%02d", minutes, remainingSeconds);
    }
}
