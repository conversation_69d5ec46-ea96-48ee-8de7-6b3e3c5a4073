package ca.xef5000.ariesCoinflip.commands;

import ca.xef5000.ariesCoinflip.AriesCoinflip;
import ca.xef5000.ariesCoinflip.managers.AnimationManager;
import ca.xef5000.ariesCoinflip.managers.CoinflipManager;
import ca.xef5000.ariesCoinflip.managers.ColorGUIManager;
import ca.xef5000.ariesCoinflip.managers.DataManager;
import ca.xef5000.ariesCoinflip.managers.GUIManager;
import ca.xef5000.ariesCoinflip.models.PlayerStats;
import ca.xef5000.ariesCoinflip.utils.ConfigManager;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.jetbrains.annotations.NotNull;

public class CoinflipCommand implements CommandExecutor {
    private final ConfigManager configManager;
    private final GUIManager guiManager;
    private final CoinflipManager coinflipManager;
    private final AnimationManager animationManager;
    private final ColorGUIManager colorGUIManager;
    private final DataManager dataManager;

    public CoinflipCommand(AriesCoinflip plugin, ConfigManager configManager, GUIManager guiManager) {
        this.configManager = configManager;
        this.guiManager = guiManager;
        this.coinflipManager = plugin.getCoinflipManager();
        this.animationManager = plugin.getAnimationManager();
        this.colorGUIManager = plugin.getColorGUIManager();
        this.dataManager = plugin.getDataManager();
    }

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage("This command can only be used by players.");
            return true;
        }

        Player player = (Player) sender;

        // Check if there are any arguments
        if (args.length > 0) {
            // Handle subcommands
            if (args[0].equalsIgnoreCase("create")) {
                if (!player.hasPermission("ariescoinflip.create")) {
                    player.sendMessage(configManager.getMessage("no_permission_create"));
                    return true;
                }
                // Handle create subcommand
                if (args.length < 3) {
                    player.sendMessage(ConfigManager.colorize("&cUsage: /coinflip create [currency] [amount]"));
                    return true;
                }

                String currency = args[1].toLowerCase();
                if (!plugin.getCurrencyManager().isValidCurrency(currency)) {
                    player.sendMessage(ConfigManager.colorize("&cInvalid currency. Available currencies: " +
                        String.join(", ", plugin.getCurrencyManager().getCurrencies().stream()
                            .map(c -> c.getName()).toArray(String[]::new))));
                    return true;
                }

                try {
                    double amount = Double.parseDouble(args[2]);
                    coinflipManager.createCoinflip(player, amount, currency);
                } catch (NumberFormatException e) {
                    player.sendMessage(ConfigManager.colorize("&cInvalid amount. Please enter a valid number."));
                }
                return true;
            } else if (args[0].equalsIgnoreCase("reload")) {
                if (!player.hasPermission("ariescoinflip.reload")) {
                    player.sendMessage(configManager.getMessage("no_permission_reload"));
                    return true;
                }
                configManager.loadConfig();
                // Reload configs for other managers
                animationManager.reloadConfigs();
                colorGUIManager.reloadConfig();
                guiManager.reloadConfig();
                player.sendMessage(configManager.getMessage("config_reloaded"));
                return true;
            } else if (args[0].equalsIgnoreCase("luck")) {
                if (!player.hasPermission("ariescoinflip.luck")) {
                    player.sendMessage(configManager.getMessage("no_permission_luck"));
                    return true;
                }

                if (args.length < 2) {
                    player.sendMessage(ConfigManager.colorize("&cUsage: /coinflip luck [get/add/remove/set] [player] <amount>"));
                    return true;
                }

                String action = args[1].toLowerCase();

                if (action.equals("get")) {
                    if (args.length < 3) {
                        // Get own luck
                        PlayerStats stats = dataManager.getPlayerStats(player);
                        player.sendMessage(configManager.getMessage("luck_get_self")
                                .replace("{luck}", String.valueOf(stats.getLuck())));
                    } else {
                        // Get another player's luck
                        String targetName = args[2];
                        if (targetName.equals("*")) {
                            player.sendMessage(ConfigManager.colorize("&cCannot get luck for all players. Please specify a player name."));
                            return true;
                        }

                        org.bukkit.OfflinePlayer target = org.bukkit.Bukkit.getOfflinePlayer(targetName);
                        PlayerStats stats = dataManager.getPlayerStats(target.getUniqueId(), target.getName());
                        player.sendMessage(configManager.getMessage("luck_get_other")
                                .replace("{player}", targetName)
                                .replace("{luck}", String.valueOf(stats.getLuck())));
                    }
                    return true;
                } else if (action.equals("add") || action.equals("remove") || action.equals("set")) {
                    if (!player.hasPermission("ariescoinflip.luck.admin")) {
                        player.sendMessage(configManager.getMessage("no_permission_luck_admin"));
                        return true;
                    }

                    if (args.length < 4) {
                        player.sendMessage(ConfigManager.colorize("&cUsage: /coinflip luck " + action + " [player] <amount>"));
                        return true;
                    }

                    String targetName = args[2];
                    int amount;

                    try {
                        amount = Integer.parseInt(args[3]);
                    } catch (NumberFormatException e) {
                        player.sendMessage(ConfigManager.colorize("&cInvalid amount. Please enter a valid number."));
                        return true;
                    }

                    if (targetName.equals("*")) {
                        // Apply to all players in database
                        int count = 0;
                        for (PlayerStats stats : dataManager.getAllPlayerStats()) {
                            if (action.equals("add")) {
                                stats.addLuck(amount);
                            } else if (action.equals("remove")) {
                                stats.removeLuck(amount);
                            } else if (action.equals("set")) {
                                stats.setLuck(amount);
                            }
                            count++;
                        }
                        dataManager.saveData();
                        player.sendMessage(configManager.getMessage("luck_update_all")
                                .replace("{count}", String.valueOf(count)));
                    } else {
                        // Apply to specific player
                        org.bukkit.OfflinePlayer target = org.bukkit.Bukkit.getOfflinePlayer(targetName);
                        PlayerStats stats = dataManager.getPlayerStats(target.getUniqueId(), target.getName());

                        if (action.equals("add")) {
                            stats.addLuck(amount);
                            player.sendMessage(configManager.getMessage("luck_add")
                                    .replace("{amount}", String.valueOf(amount))
                                    .replace("{player}", targetName)
                                    .replace("{luck}", String.valueOf(stats.getLuck())));
                        } else if (action.equals("remove")) {
                            stats.removeLuck(amount);
                            player.sendMessage(configManager.getMessage("luck_remove")
                                    .replace("{amount}", String.valueOf(amount))
                                    .replace("{player}", targetName)
                                    .replace("{luck}", String.valueOf(stats.getLuck())));
                        } else if (action.equals("set")) {
                            stats.setLuck(amount);
                            player.sendMessage(configManager.getMessage("luck_set")
                                    .replace("{player}", targetName)
                                    .replace("{amount}", String.valueOf(amount)));
                        }
                        dataManager.saveData();
                    }
                    return true;
                } else {
                    player.sendMessage(ConfigManager.colorize("&cInvalid action. Use get, add, remove, or set."));
                    return true;
                }
            }
        }

        // Default: Open the main coinflip menu
        if (!player.hasPermission("ariescoinflip.use")) {
            player.sendMessage(configManager.getMessage("no_permission"));
            return true;
        }
        guiManager.openMainMenu(player);
        return true;
    }
}
