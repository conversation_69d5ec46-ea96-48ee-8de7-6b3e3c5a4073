package ca.xef5000.ariesCoinflip.models.currencies;

import ca.xef5000.ariesCoinflip.AriesCoinflip;

import java.util.function.Function;

public enum CurrencyIntegrations {
    VAULT(plugin -> new VaultCurrencyIntegration(plugin));
    // Add more integrations as needed

    private final Function<AriesCoinflip, CurrencyIntegration> factory;

    CurrencyIntegrations(Function<AriesCoinflip, CurrencyIntegration> factory) {
        this.factory = factory;
    }

    public CurrencyIntegration createIntegration(AriesCoinflip plugin) {
        return factory.apply(plugin);
    }
}
