package ca.xef5000.ariesCoinflip.managers;

import ca.xef5000.ariesCoinflip.AriesCoinflip;
import ca.xef5000.ariesCoinflip.utils.ConfigManager;
import org.bukkit.Bukkit;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.AsyncPlayerChatEvent;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

public class CoinflipManager implements Listener {
    private final AriesCoinflip plugin;
    private final ConfigManager configManager;
    private final DataManager dataManager;


    public CoinflipManager(AriesCoinflip plugin, ConfigManager configManager, DataManager dataManager) {
        this.plugin = plugin;
        this.configManager = configManager;
        this.dataManager = dataManager;

        // Register this as a listener
        Bukkit.getPluginManager().registerEvents(this, plugin);
    }

    /**
     * Creates a coinflip directly without requiring chat interaction
     *
     * @param player The player creating the coinflip
     * @param amount The amount to bet
     * @param currency The currency to use
     */
    public void createCoinflip(Player player, double amount, String currency) {
        // Check if player already has an active offer
        if (dataManager.hasActiveOffer(player.getUniqueId())) {
            player.sendMessage(ConfigManager.colorize("&cYou already have an active coinflip offer."));
            return;
        }

        // Get currency object and validate
        ca.xef5000.ariesCoinflip.models.currencies.Currency currencyObj = plugin.getCurrencyManager().getCurrency(currency);
        if (currencyObj == null) {
            player.sendMessage(ConfigManager.colorize("&cInvalid currency: " + currency));
            return;
        }

        // Check minimum bet for this currency
        if (amount < currencyObj.getMinBet()) {
            player.sendMessage(configManager.getMessage("minimum_bet")
                    .replace("{min_bet}", String.format("%.2f", currencyObj.getMinBet())));
            return;
        }

        // Check maximum bet for this currency
        if (amount > currencyObj.getMaxBet()) {
            player.sendMessage(configManager.getMessage("maximum_bet")
                    .replace("{max_bet}", String.format("%.2f", currencyObj.getMaxBet())));
            return;
        }

        // Check if player has enough money in this currency
        if (currencyObj.getIntegration().getBalance(player) < amount) {
            player.sendMessage(configManager.getMessage("insufficient_funds"));
            return;
        }

        // Open color selection GUI instead of immediately creating the coinflip
        plugin.getColorGUIManager().openCreatorColorGUI(player, amount, currency);
    }

    /**
     * Legacy method for backward compatibility (uses default currency)
     */
    @Deprecated
    public void createCoinflip(Player player, double amount) {
        createCoinflip(player, amount, "money");
    }
}
